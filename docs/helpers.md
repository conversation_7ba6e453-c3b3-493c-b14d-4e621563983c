# Helper Functions Documentation

This document provides detailed documentation for the helper functions used in the Universal AI Connector extension.

## `processSystemPrompt(prompt)`

A utility function that processes the system prompt by replacing variables with their actual values.

### Parameters
- `prompt` (string): The system prompt text that may contain variables to be replaced.

### Variables Supported
- `%datetime%`: Replaced with the current date and time in the format "YYYY-MM-DD HH:MM:SS".

### Returns
- (string): The processed prompt with all variables replaced with their actual values.

### Implementation Details
- Uses JavaScript's `Date` object to get the current date and time.
- Formats the date using `toISOString()` and string manipulation to achieve the desired format.
- Uses regular expression with global flag (`/%datetime%/g`) to replace all occurrences of the variable.

### Example
```javascript
// Input
const prompt = "Current time: %datetime%. Please provide information about...";

// Output after processing
// "Current time: 2025-05-19 22:00:21. Please provide information about..."
```

### Usage Context
This function is used to dynamically update system prompts with time-sensitive information before sending them to AI models. This ensures that the AI has accurate temporal context when responding to user queries.

### Dependencies
- No external dependencies, uses only native JavaScript Date functionality.

### Module Exports
The function is exported as part of a module when used in a Node.js environment:
```javascript
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { processSystemPrompt };
}
```
