# Content Script Functions Documentation

This document provides detailed documentation for the functions in the content script (`content.js`) of the Universal AI Connector extension.

## State Management

The content script maintains state through the `contentState` object with the following properties:

```javascript
const contentState = {
  pageContent: '',         // Extracted text content from the page
  selectedText: '',        // User-selected text on the page
  initialized: false,      // Whether initial content extraction has completed
  panelOpen: false,        // Whether the extension panel is currently open
  contentTimer: null,      // Timer for periodic content updates
  lastExtracted: 0,        // Timestamp of last content extraction
  isContextInvalidated: false // Flag to track if extension context is invalid
};
```

## `safeExecuteChromeAPI(apiCall, fallback = null)`

Safely executes Chrome extension API calls with proper error handling.

### Parameters
- `apiCall` (function): The Chrome API function to execute.
- `fallback` (any, optional): Value to return if the API call fails. Defaults to `null`.

### Returns
- Result of the API call if successful, otherwise the fallback value.

### Implementation Details
- Checks if Chrome APIs are available before attempting to call them.
- Sets the `isContextInvalidated` flag if a context invalidation error occurs.
- Stops content updates if the context is invalidated.

## `isContextValid()`

Checks if the extension context is still valid.

### Returns
- (boolean): `true` if the extension context is valid, `false` otherwise.

### Implementation Details
- Verifies that Chrome runtime APIs are accessible and the extension ID exists.

## `extractPageContent()`

Extracts text content from the current web page using multiple fallback methods.

### Returns
- (string): The extracted page content, or an empty string if extraction fails.

### Implementation Details
- Checks for context validity before attempting DOM access.
- Uses multiple methods to extract content: `innerText`, `textContent`.
- Updates the `contentState` object with extracted content and metadata.
- Stores content in Chrome local storage for access by other extension components.
- Automatically sends content to the background script.
- Sets appropriate flags if context invalidation occurs during extraction.

## `sendContentToBackground()`

Sends the extracted page content to the background script.

### Implementation Details
- Uses Chrome messaging to send content to the background script.
- Includes page content, selected text, and timestamp in the message.
- Implements retry logic with exponential backoff for reliability.
- Handles context invalidation gracefully.

## `startContentUpdates()`

Starts periodic content updates when the panel is open.

### Implementation Details
- Sets up an interval to extract content periodically.
- Uses adaptive timing based on content size and change frequency.
- Includes safeguards to prevent excessive updates.
- Cleans up previous timers to prevent memory leaks.

## `stopContentUpdates()`

Stops periodic content updates when the panel is closed or context is lost.

### Implementation Details
- Clears the content update timer.
- Updates the `contentState` to reflect that the panel is closed.

## `setupMessageListener()`

Sets up a listener for messages from the background script or panel.

### Implementation Details
- Handles various message actions:
  - `extractContent`: Extracts page content on demand.
  - `getSelectedText`: Returns the currently selected text.
  - `panelOpened`: Starts content updates when the panel opens.
  - `panelClosed`: Stops content updates when the panel closes.
  - `invalidateContext`: Handles context invalidation instructions.
- Implements error handling for each message type.
- Returns appropriate responses to the sender.

## `debounce(func, wait)`

Utility function to prevent too many function calls in a short period.

### Parameters
- `func` (function): The function to debounce.
- `wait` (number): The time in milliseconds to wait before allowing another function call.

### Returns
- (function): A debounced version of the input function.

### Implementation Details
- Uses JavaScript closures to maintain state between calls.
- Clears previous timeouts to prevent multiple executions.
- Preserves the execution context (`this`) and arguments.

## Event Listeners

The content script sets up several event listeners:

1. **Page Load**: Extracts content when the page first loads.
2. **Window Focus**: Re-extracts content when the tab regains focus.
3. **Selection Change**: Tracks text selection changes when the panel is open.
4. **DOM Mutations**: Uses MutationObserver to detect page content changes.

### Implementation Details
- Event listeners are set up with appropriate error handling.
- The MutationObserver uses debouncing to prevent excessive processing.
- Selection changes are tracked only when the panel is open to conserve resources.
