# Panel Script Functions Documentation

This document provides detailed documentation for the functions in the panel script (`panel.js`) of the Universal AI Connector extension.

## Debug Utilities

### `debugLog(...args)`
Logs debug messages to the console when debugging is enabled.

#### Parameters
- `...args`: Arguments to log to the console.

#### Implementation Details
- Only logs if the `DEBUG` constant is set to `true`.

### `debugWarn(...args)`
Logs warning messages to the console when debugging is enabled.

#### Parameters
- `...args`: Warning arguments to log to the console.

#### Implementation Details
- Only logs if the `DEBUG` constant is set to `true`.
- Uses `console.warn`.

### `debugError(...args)`
Logs error messages to the console when debugging is enabled.

#### Parameters
- `...args`: Error arguments to log to the console.

#### Implementation Details
- Only logs if the `DEBUG` constant is set to `true`.
- Uses `console.error`.

## Message Display

### `displayMessage(message, type = 'assistant')`
Displays a message in the chat interface.

#### Parameters
- `message` (string): The message content to display.
- `type` (string, optional): The type of message ('assistant', 'user', or 'system'). Defaults to 'assistant'.

#### Returns
- (HTMLElement): The created message element.

#### Implementation Details
- Creates a new message element with appropriate CSS classes.
- Converts markdown to HTML for assistant messages using the `marked` library.
- Uses plain text for user messages.
- Scrolls the message container to show the new message.
- Adds copy buttons to code blocks in the message.

## Markdown Processing

### `initializeMarked()`
Initializes the marked.js library with custom rendering options.

#### Returns
- (Object): The initialized marked instance.

#### Implementation Details
- Sets up syntax highlighting for code blocks using highlight.js.
- Configures custom renderers for code blocks to add copy buttons.
- Handles edge cases like streaming content and partial code blocks.
- Sets security options to prevent XSS vulnerabilities.

### `escapeHtml(unsafe)`
Helper function to escape HTML special characters.

#### Parameters
- `unsafe` (string): The string containing potentially unsafe HTML characters.

#### Returns
- (string): The input string with HTML special characters escaped.

#### Implementation Details
- Replaces `&`, `<`, `>`, `"`, and `'` with their HTML entity equivalents.
- Used to prevent XSS attacks when rendering user-provided content.

## UI Utilities

### `isScrolledToBottom(container)`
Checks if a scrollable container is scrolled to the bottom.

#### Parameters
- `container` (HTMLElement): The scrollable container to check.

#### Returns
- (boolean): `true` if the container is scrolled to the bottom, `false` otherwise.

#### Implementation Details
- Compares the scroll position to the scroll height minus the visible height.
- Includes a small threshold (1px) to account for rounding errors.

## Port Connection

### `setupPortConnection()`
Initializes the port connection for streaming responses from the background script.

#### Implementation Details
- Creates a long-lived connection to the background script.
- Sets up message listeners for streaming tokens, completion, and errors.
- Handles port disconnection gracefully.

## Stream Handling

### `handleStreamResponse(message)`
Handles streamed responses from the background script.

#### Parameters
- `message` (object): The message received from the background script.

#### Implementation Details
- Processes different message types:
  - `token`: Updates the UI with a new token.
  - `complete`: Finalizes the streaming response.
  - `error`: Displays an error message.
- Manages streaming state variables.

### `updateStreamingMessage(content)`
Updates the UI with streamed content.

#### Parameters
- `content` (string): The content to display.

#### Implementation Details
- Updates the current streaming message with new content.
- Converts markdown to HTML using the `marked` library.
- Handles code blocks and syntax highlighting.
- Maintains scroll position based on user scrolling behavior.
- Adds copy buttons to code blocks.

### `finishStreamingResponse(errorMessage = null)`
Finalizes a streaming response and updates the UI state.

#### Parameters
- `errorMessage` (string, optional): Error message to display if the streaming failed.

#### Implementation Details
- Updates chat history with the completed message.
- Resets streaming state variables.
- Displays an error message if provided.
- Re-enables the input field and send button.

## Panel State Management

### `notifyContentScriptPanelOpened()`
Notifies the content script that the panel has been opened.

#### Implementation Details
- Sends a message to the content script to start content updates.
- Handles errors if the content script is not available.
- Updates the panel state to reflect that it's open.

### `handleVisibilityChange()`
Handles visibility changes to detect when the panel is closed.

#### Implementation Details
- Detects when the document becomes hidden (panel closed).
- Notifies the content script to stop content updates.
- Updates the panel state to reflect that it's closed.
- Cleans up resources to prevent memory leaks.

## Theme Management

### `loadThemePreference()`
Loads the user's theme preference from storage.

#### Implementation Details
- Retrieves the theme preference from Chrome storage.
- Applies the theme based on the stored preference.
- Sets a default theme if no preference is stored.

### `toggleDarkMode()`
Toggles between light and dark mode.

#### Implementation Details
- Switches the current theme preference.
- Updates the UI to reflect the new theme.
- Saves the preference to Chrome storage.

### `applyTheme()`
Applies the current theme to the UI.

#### Implementation Details
- Adds or removes the 'dark-mode' class from the body element.
- Updates the theme toggle button icon.
- Adjusts UI elements for the selected theme.

## Code Block Handling

### `addCopyButtonsToCodeBlocks(container = document)`
Adds copy buttons to code blocks in the specified container.

#### Parameters
- `container` (HTMLElement, optional): The container to process. Defaults to the entire document.

#### Implementation Details
- Finds all code blocks in the container.
- Adds a copy button to each code block.
- Sets up event listeners for the copy buttons.
- Handles edge cases like nested code blocks.

### `handleNewMessage(messageElement)`
Processes a new message element to add copy buttons to its code blocks.

#### Parameters
- `messageElement` (HTMLElement): The message element to process.

#### Implementation Details
- Finds all code blocks in the message element.
- Adds copy buttons to each code block.
- Ensures proper formatting and styling.

## Message Management

### `addMessage(content, role)`
Adds a message to the UI with markdown support and caching.

#### Parameters
- `content` (string): The message content.
- `role` (string): The role of the message sender ('user', 'assistant', or 'system').

#### Returns
- (string): The ID of the created message.

#### Implementation Details
- Creates a unique ID for the message.
- Uses a cache to avoid re-rendering identical messages.
- Renders markdown to HTML using the `marked` library.
- Manages cache size to prevent memory issues.
- Scrolls to show the new message.

### `removeMessage(messageId)`
Removes a message from the UI.

#### Parameters
- `messageId` (string): The ID of the message to remove.

#### Implementation Details
- Finds all messages with the specified ID.
- Prioritizes removing assistant messages if multiple matches exist.
- Handles edge cases gracefully.

### `downloadLatestAiResponse()`
Downloads the latest AI response as a markdown file.

#### Implementation Details
- Finds the most recent assistant message in the chat history.
- Creates a Blob with the message content.
- Generates a download link and triggers the download.
- Cleans up temporary elements after download.
- Provides feedback if no AI response is available.
