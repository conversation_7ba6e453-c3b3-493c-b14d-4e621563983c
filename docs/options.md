# Options Script Functions Documentation

This document provides detailed documentation for the functions in the options script (`options.js`) of the Universal AI Connector extension.

## Configuration Management

### `parseContextSize(value)`
Parses a user-friendly context size string into a numeric value.

#### Parameters
- `value` (string): The context size string to parse, which may include suffixes like 'K' or 'KB'.

#### Returns
- (number): The parsed context size as an integer.

#### Implementation Details
- Removes commas and spaces from the input.
- Handles 'KB' suffix (binary - multiplies by 1024).
- Handles 'K' suffix (decimal - multiplies by 1000).
- Falls back to direct integer parsing if no suffix is present.

### `loadSettings()`
Loads saved settings from Chrome's sync storage.

#### Implementation Details
- Retrieves settings from Chrome's sync storage.
- Populates form fields with saved values.
- Falls back to default values for missing settings.
- Handles special cases like API key visibility and endpoint-specific settings.
- Updates the UI to reflect loaded settings.

### `saveSettings(e)`
Saves the current settings to Chrome's sync storage.

#### Parameters
- `e` (Event): The form submission event.

#### Implementation Details
- Prevents the default form submission behavior.
- Validates form inputs before saving.
- Parses and normalizes the context size value.
- Collects all settings from form fields.
- Saves settings to Chrome's sync storage.
- Displays a success message to the user.
- Handles errors gracefully with appropriate user feedback.

### `resetSettings()`
Resets all settings to their default values.

#### Implementation Details
- Populates form fields with default values from the `defaultSettings` object.
- Does not save to storage until the user explicitly clicks the save button.
- Updates dependent UI elements to reflect the default values.
- Provides user feedback about the reset action.

### `updateApiKeyRequired()`
Updates the API key field's required attribute based on the API key required checkbox.

#### Implementation Details
- Gets the current state of the API key required checkbox.
- Updates the `required` attribute of the API key input field accordingly.
- Updates the visual indication of the field's required status.
- Handles the case where the API key is empty but not required.

## Theme Management

### `loadThemePreference()`
Loads the user's theme preference from storage.

#### Implementation Details
- Retrieves the theme preference from Chrome storage.
- Sets the global `darkMode` variable based on the stored preference.
- Applies the theme using the `applyTheme()` function.

### `toggleDarkMode()`
Toggles between light and dark mode.

#### Implementation Details
- Inverts the current `darkMode` value.
- Applies the new theme using the `applyTheme()` function.
- Saves the new preference to Chrome storage.

### `applyTheme()`
Applies the current theme to the UI.

#### Implementation Details
- Adds or removes the 'dark-mode' class from the body element.
- Updates the theme toggle button icon.
- Updates the appearance of form elements and containers.

## API Testing

### `testEndpoint()`
Tests the configured endpoint with a sample request.

#### Implementation Details
- Collects the current endpoint, model, and API key from form fields.
- Constructs an appropriate API request based on the endpoint type.
- Handles different API formats (OpenAI, Anthropic, local models).
- Sends a minimal test request to verify connectivity.
- Displays the test result to the user.
- Provides detailed error messages with troubleshooting suggestions.
- Handles network errors and unexpected issues gracefully.

## UI Utilities

### `showStatus(message, type, duration = 3000)`
Shows a status message to the user.

#### Parameters
- `message` (string): The message to display.
- `type` (string): The type of message ('success', 'error', or 'info').
- `duration` (number, optional): How long to show the message in milliseconds. Defaults to 3000ms.

#### Implementation Details
- Updates the status message element with the provided text.
- Applies appropriate CSS classes based on the message type.
- Makes the message visible with full opacity.
- Automatically fades out the message after the specified duration.
- Cleans up the message element after the fade-out animation completes.

## Event Listeners

The options script sets up several event listeners:

1. **Form Submission**: Handles saving settings when the form is submitted.
2. **Reset Button**: Resets settings to defaults when clicked.
3. **API Key Toggle**: Toggles visibility of the API key field.
4. **Theme Toggle**: Switches between light and dark mode.
5. **Test Endpoint Button**: Tests the configured endpoint.
6. **Endpoint Info**: Shows/hides information about endpoint configuration.
7. **Context Size Validation**: Validates the context size input on blur.
8. **Endpoint Input**: Updates the API key required checkbox based on the endpoint.

### Implementation Details
- Event listeners are set up after the DOM content is loaded.
- Form validation is performed both on blur and before submission.
- The endpoint info popover includes click-outside detection to close it.
- The context size input includes custom validation logic with user feedback.
