# Background Script Functions Documentation

This document provides detailed documentation for the functions in the background script (`background.js`) of the Universal AI Connector extension.

## Debug Utilities

### `debugLog(...args)`
Logs debug messages to the console when debugging is enabled.

#### Parameters
- `...args`: Arguments to log to the console.

#### Implementation Details
- Only logs if the `DEBUG` constant is set to `true`.
- Prefixes messages with `[Background]` for easy identification.

### `debugWarn(...args)`
Logs warning messages to the console when debugging is enabled.

#### Parameters
- `...args`: Warning arguments to log to the console.

#### Implementation Details
- Only logs if the `DEBUG` constant is set to `true`.
- Uses `console.warn` with `[Background]` prefix.

### `debugError(...args)`
Logs error messages to the console when debugging is enabled.

#### Parameters
- `...args`: Error arguments to log to the console.

#### Implementation Details
- Only logs if the `DEBUG` constant is set to `true`.
- Uses `console.error` with `[Background]` prefix.

## Service Worker Management

### `initializeServiceWorker()`
Initializes the service worker and sets up alarms to keep it alive.

#### Implementation Details
- Creates a keep-alive alarm that triggers every 30 seconds.
- Configures the side panel to open when the extension icon is clicked.
- Uses Chrome's `sidePanel.setPanelBehavior()` API.

## Configuration Management

### `isConfigurationComplete(config)`
Validates whether the extension configuration is complete and usable.

#### Parameters
- `config` (object): The configuration object to validate.

#### Returns
- (boolean): `true` if the configuration is complete, `false` otherwise.

#### Implementation Details
- Checks different scenarios based on endpoint type:
  - For OpenAI endpoints: Requires a valid API key.
  - For local endpoints (localhost/127.0.0.1): No API key required.
  - For other endpoints: Follows the `apiKeyRequired` setting.

### `loadSavedSettings()`
Loads saved settings from Chrome's sync storage.

#### Implementation Details
- Retrieves settings from Chrome's sync storage.
- Updates the global `config` object with saved values.
- Maintains default values for missing settings.
- Validates loaded configuration.

## First Run Experience

### `checkFirstRun()`
Checks if this is the first time the extension is running and sets up first-run experience.

#### Implementation Details
- Checks for the `firstRunCompleted` flag in sync storage.
- Opens the options page with a first-run parameter if this is the first run.
- Sets the `firstRunCompleted` flag to prevent future first-run experiences.

## API Handling

### `safeApiCall(config, apiCallFunction, ...args)`
Safely executes API calls with proper error handling and configuration validation.

#### Parameters
- `config` (object): The configuration to use for the API call.
- `apiCallFunction` (function): The API function to call.
- `...args`: Arguments to pass to the API function.

#### Returns
- (Promise): Resolves to the API call result or an error object.

#### Implementation Details
- Validates configuration completeness before making the API call.
- Wraps the API call in a try-catch block for error handling.
- Returns appropriate error messages if the call fails.

## System Prompt Processing

### `processSystemPrompt(prompt)`
Processes the system prompt by replacing variables with their values.

#### Parameters
- `prompt` (string): The system prompt to process.

#### Returns
- (string): The processed system prompt with variables replaced.

#### Implementation Details
- Replaces `%datetime%` with the current date and time.
- Formats the date in "YYYY-MM-DD HH:MM:SS" format.

## Message Handling

### `safeSend(responseFunc, label)`
Safely sends responses to avoid unchecked runtime.lastError.

#### Parameters
- `responseFunc` (function): Function that sends the response.
- `label` (string): Label for debugging purposes.

#### Implementation Details
- Wraps the response function in a try-catch block.
- Logs errors if the send operation fails.

## Context Menu Setup

### `setupContextMenu()`
Sets up the extension's context menu items.

#### Implementation Details
- Creates context menu items for different actions.
- Handles different contexts (selection, page, etc.).
- Sets appropriate titles and contexts for each item.

## Tab Content Management

### `getTabContentAndRespond(tabId, responseCallback)`
Retrieves content for a specific tab and sends it via the provided callback.

#### Parameters
- `tabId` (number): The ID of the tab to get content from.
- `responseCallback` (function): Callback to send the content response.

#### Implementation Details
- Uses a multi-layer approach to retrieve content:
  1. First checks local cache.
  2. Then tries Chrome storage.
  3. Finally, sends a message to the content script.
- Implements fallbacks and error handling for each method.
- Sends the content via the provided callback.

### `refreshTabContent(tabId, forceRefresh, callback)`
Refreshes the content for a specific tab.

#### Parameters
- `tabId` (number): The ID of the tab to refresh content for.
- `forceRefresh` (boolean): Whether to force a refresh from the content script.
- `callback` (function): Callback to receive the refreshed content.

#### Implementation Details
- Sends a message to the content script to extract fresh content.
- Updates the local cache with the new content.
- Calls the provided callback with the refreshed content.

## LLM API Integration

### `streamLLMResponse(port, message)`
Streams LLM responses through a provided port connection.

#### Parameters
- `port` (Port): The port connection to stream responses through.
- `message` (object): The message containing the query and context.

#### Implementation Details
- Constructs the appropriate API request based on the configured endpoint.
- Handles different API formats (OpenAI, Anthropic, local models).
- Processes streaming responses in chunks.
- Sends tokens to the panel as they arrive.
- Implements fallback for non-streaming responses.
- Handles errors and notifies the panel of completion or errors.

## Event Listeners

The background script sets up several event listeners:

1. **Tab Removal**: Cleans up cached content when tabs are closed.
2. **Tab Updates**: Detects navigation events and refreshes content.
3. **Alarms**: Maintains service worker activity.
4. **Storage Changes**: Updates configuration when settings change.
5. **Context Menu Clicks**: Handles user interactions with context menu items.
6. **Runtime Messages**: Processes messages from content scripts and panel.
7. **Connection Requests**: Sets up streaming connections for LLM responses.

### Implementation Details
- Event listeners include appropriate error handling.
- Tab events manage the content cache to prevent memory leaks.
- Storage changes trigger configuration reloads.
- Message handling includes validation and security checks.
