# Universal AI Connector Documentation

This documentation provides detailed information about the Universal AI Connector browser extension, which allows users to interact with various AI models through a unified interface.

## Overview

The Universal AI Connector is a browser extension that enables users to:
- Connect to various AI models and APIs (OpenAI, local models, etc.)
- Extract and use web page content as context for AI queries
- Stream AI responses in real-time
- Configure various settings for AI interactions

## Components

The extension consists of several key components:

1. **Content Script** (`content.js`): Extracts content from web pages and manages communication with the background script.
2. **Background Script** (`background.js`): Handles API calls, manages extension state, and coordinates between content scripts and the panel.
3. **Panel Script** (`panel.js`): Manages the UI for the side panel where users interact with the AI.
4. **Cache Manager** (`cache-manager.js`, `background-cache-manager.js`): Provides centralized control for registering, clearing, and managing caches throughout the application.
5. **Helpers** (`helpers.js`): Contains utility functions used across the extension.
6. **Options Script** (`options.js`): Manages the options/settings page for the extension.

## Function Documentation

Detailed documentation for each component is available in the following files:

- [Content Script Functions](./content.md)
- [Background Script Functions](./background.md)
- [Panel Script Functions](./panel.md)
- [Helper Functions](./helpers.md)
- [Options Script Functions](./options.md)
- [Cache Manager](./cache-manager.md) - Centralized cache management system
- [Memory Bank](./memorybank.md) - Project progress tracking and implementation details
