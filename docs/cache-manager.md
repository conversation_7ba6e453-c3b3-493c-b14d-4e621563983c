# Cache Manager Documentation

This document provides detailed documentation for the Cache Manager system in the Universal AI Connector extension.

## Overview

The Cache Manager is a singleton class that provides centralized control for registering, clearing, and managing caches throughout the application. It was refactored to consolidate cache clearing functionality in one place, eliminating the need to search across the codebase to clear caches.

There are two implementations of the Cache Manager:
1. **CacheManager** (`cache-manager.js`) - Used in the foreground/panel context
2. **BackgroundCacheManager** (`background-cache-manager.js`) - Used in the background script context

Both implementations share a similar API and functionality but operate in different contexts.

## Key Features

- **Centralized Cache Registry**: Maintains a registry of all caches in the application
- **Group-based Organization**: Caches can be organized into logical groups
- **Selective Clearing**: Clear individual caches, groups of caches, or all caches
- **Event System**: Provides event listeners for cache operations
- **Debugging Support**: Comprehensive logging for cache operations

## API Reference

### Registration

#### `register(key, cache, clearMethod, group = 'default')`

Registers a cache with the manager.

**Parameters:**
- `key` (string): Unique identifier for the cache
- `cache` (Object): The cache object or reference
- `clearMethod` (Function): Method to clear this specific cache
- `group` (string, optional): Group name for categorizing caches (defaults to 'default')

**Returns:**
- (boolean): Success status

**Example:**
```javascript
import cacheManager from './cache-manager.js';

// Register a cache
cacheManager.register(
  'pageContext',  // Unique key
  contextObject,  // The cache object
  (cache) => {    // Clear method
    cache.url = '';
    cache.title = '';
    cache.content = '';
    cache.selection = '';
    cache.timestamp = 0;
  },
  'context'       // Group name
);
```

### Clearing Caches

#### `clearCache(key)`

Clears a specific cache by key.

**Parameters:**
- `key` (string): The cache key to clear

**Returns:**
- (boolean): Success status

**Example:**
```javascript
// Clear a specific cache
cacheManager.clearCache('pageContext');
```

#### `clearGroup(group)`

Clears all caches in a specific group.

**Parameters:**
- `group` (string): The group name to clear

**Returns:**
- (Object): Results with success count and failures

**Example:**
```javascript
// Clear all caches in the 'context' group
const results = cacheManager.clearGroup('context');
console.log(`Cleared ${results.success} caches, ${results.failures} failures`);
```

#### `clearAll()`

Clears all registered caches.

**Returns:**
- (Object): Results with success count and failures

**Example:**
```javascript
// Clear all caches
const results = cacheManager.clearAll();
console.log(`Cleared ${results.success} caches, ${results.failures} failures`);
```

### Event Handling

#### `addListener(event, callback)`

Adds a listener for cache events.

**Parameters:**
- `event` (string): Event name ('clear', 'clearGroup', 'clearAll', or 'all')
- `callback` (Function): Callback function

**Returns:**
- (Function): Function to remove the listener

**Example:**
```javascript
// Listen for all cache clear events
const removeListener = cacheManager.addListener('clear', (data) => {
  console.log(`Cache ${data.key} was cleared`);
});

// Later, when you want to stop listening
removeListener();
```

### Information

#### `getRegistryInfo()`

Gets information about registered caches.

**Returns:**
- (Object): Cache registry information including total count and groups

**Example:**
```javascript
// Get information about registered caches
const info = cacheManager.getRegistryInfo();
console.log(`Total caches: ${info.totalCaches}`);
console.log('Groups:', info.groups);
```

## Usage Examples

### Registering and Clearing Page Context

```javascript
import cacheManager from './cache-manager.js';

// Store the current page context
let currentPageContext = {
  url: '',
  title: '',
  content: '',
  selection: '',
  timestamp: 0
};

// Register with cache manager
cacheManager.register(
  'pageContext', 
  currentPageContext,
  (cache) => {
    // Reset the context object properties
    cache.url = '';
    cache.title = '';
    cache.content = '';
    cache.selection = '';
    cache.timestamp = 0;
  },
  'context'
);

// Later, to clear the context
function clearPageContext() {
  // Use the cache manager to clear the context
  cacheManager.clearCache('pageContext');
  
  // Additional operations after clearing...
}
```

### Clearing All Caches During Chat Reset

```javascript
import cacheManager from './cache-manager.js';

async function clearChat() {
  try {
    // Update UI elements
    updateUIAfterClear();
    
    // Clear page context
    clearPageContext();
    
    // Clear markdown cache
    clearMarkdownCache();
    
    // Clear all caches using the cache manager
    const results = cacheManager.clearAll();
    console.log('All caches cleared:', results);
    
  } catch (error) {
    console.error('Error clearing chat:', error);
  }
}
```

## Implementation Details

### Singleton Pattern

Both cache manager implementations use the singleton pattern to ensure there's only one instance managing caches in each context:

```javascript
// Create and export singleton instance
const cacheManagerInstance = new CacheManager();
export default cacheManagerInstance;
```

### Registry Structure

The cache registry is implemented as a Map where:
- Keys are unique string identifiers
- Values are objects containing:
  - `cache`: The cache object reference
  - `clearMethod`: Function to clear this cache
  - `group`: Group name for categorization

### Event System

The event system uses a Map of Sets to store event listeners:
- Keys are event names ('clear', 'clearGroup', 'clearAll', 'all')
- Values are Sets of callback functions

When a cache operation occurs, all registered listeners for that event are notified with relevant data.

## Best Practices

1. **Consistent Keys**: Use descriptive, consistent naming for cache keys
2. **Logical Grouping**: Group related caches together for easier management
3. **Efficient Clear Methods**: Implement clear methods that properly reset cache state without unnecessary operations
4. **Error Handling**: Always check the return value of cache operations for success/failure
5. **Event Listeners**: Clean up event listeners when they're no longer needed

## Integration with Other Components

The Cache Manager is integrated with several components in the application:

- **Panel Context**: Registers and clears page context
- **Panel UI**: Clears markdown rendering cache
- **Panel**: Centralizes clearing of all caches during chat reset
- **Background Script**: Manages background-specific caches

## Testing

The Cache Manager functionality is tested in `tests/context-clearing.test.js`, which verifies:

1. Proper registration of caches
2. Successful clearing of individual caches
3. Group-based clearing
4. Clearing all caches
5. Event notification