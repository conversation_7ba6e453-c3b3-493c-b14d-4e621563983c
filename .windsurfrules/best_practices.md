# Windsurf Rules File: Best Programming Practices

## General Principles
- Write clean, readable, and maintainable code.
- Follow language-specific style guides and conventions.
- Use meaningful and descriptive names for variables, functions, classes, and other identifiers.
- Include comments where necessary to explain why (not what) the code does.
- Avoid code duplication by reusing code through functions, classes, or modules.
- Write modular and reusable code components.
- Handle errors and exceptions gracefully.
- Ensure code is efficient and optimized without sacrificing readability.

## Code Structure
- Organize code logically and consistently.
- Use proper indentation and spacing.
- Separate concerns (e.g., UI, business logic, data access).
- Limit function length; ideally, each function should do one thing.
- Use consistent naming conventions (camelCase, snake_case, PascalCase) appropriate to the language.

## Documentation
- Add docstrings or comments for all functions, classes, and modules.
- Document parameters, return values, and exceptions thrown.
- Maintain up-to-date README files for projects.

## Testing
- Write unit tests covering critical and edge cases.
- Use test-driven development (TDD) when possible.
- Ensure tests are automated and easy to run.
- Maintain high test coverage.

## Version Control
- Write clear, concise commit messages.
- Make small, atomic commits.
- Use branches effectively for features, fixes, and experiments.

## Security
- Validate and sanitize all inputs.
- Avoid hardcoding sensitive information like passwords or API keys.
- Follow best practices for authentication and authorization.
- Keep dependencies up to date to avoid vulnerabilities.

## Performance
- Avoid premature optimization.
- Profile and benchmark critical code sections.
- Use efficient data structures and algorithms.

## Collaboration
- Follow team or community coding standards.
- Review code with peers through pull requests.
- Be open to feedback and willing to improve code quality.

---

*This rules file is intended to guide LLMs in producing code that adheres to industry best practices, ensuring quality, maintainability, and security.*
