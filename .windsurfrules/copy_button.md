Implementing a Copy Icon for Code Blocks
To add a "copy" icon beside code blocks returned by an LLM endpoint, you'll need to:

Add a copy button/icon to each code block
Implement clipboard functionality
Provide user feedback when copying succeeds
Here's a complete implementation:

```html
<!-- Add this CSS to your stylesheet -->
<style>
  .code-container {
    position: relative;
  }
  
  .copy-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 4px 8px;
    background-color: #f1f1f1;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;    
  }
  .code-container:hover .copy-btn {
    background-color: #e0e0e0;
  }
</style>
```

```javascript
// Function to process code blocks and add copy buttons
function addCopyButtonsToCodeBlocks() {
  // Find all pre code elements
  const codeBlocks = document.querySelectorAll('pre code');
  
  codeBlocks.forEach((codeBlock) => {
    // Create container div
    const container = document.createElement('div');
    container.className = 'code-container';
    
    // Create copy button
    const copyButton = document.createElement('button');
    copyButton.className = 'copy-btn';
    copyButton.textContent = 'Copy';
    
    // Add click event to copy code
    copyButton.addEventListener('click', () => {
      const code = codeBlock.textContent;
      navigator.clipboard.writeText(code)
        .then(() => {
          // Show success feedback
          copyButton.textContent = 'Copied!';
          setTimeout(() => {
            copyButton.textContent = 'Copy';
          }, 2000);
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
        });
    });
    
    // Replace the code block with our container
    const parent = codeBlock.parentNode;
    const wrapper = parent.parentNode;
    wrapper.replaceChild(container, parent);
    
    // Add the original code block and the copy button to container
    container.appendChild(parent);
    container.appendChild(copyButton);
  });
}

// Call the function when content is loaded
document.addEventListener('DOMContentLoaded', addCopyButtonsToCodeBlocks);

// If code blocks are dynamically added after page load,
// you'll need to call this function again after the new content is added
```

This code will:

1. Find all code blocks in the document
2. Wrap each code block in a container
3. Add a "Copy" button in the top-right corner
4. Implement clipboard functionality using the Navigator Clipboard API
5. Show visual feedback ("Copied!") when copying succeeds

For dynamically loaded content from an LLM endpoint, make sure to call the `addCopyButtonsToCodeBlocks()` function after the new content is inserted into the DOM.


========================================================================
