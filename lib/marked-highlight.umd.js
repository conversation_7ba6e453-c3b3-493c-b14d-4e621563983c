/*! marked-highlight v2.0.1 | MIT License | github.com/markedjs/marked-highlight */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.markedHighlight = factory());
})(this, (function () {
  'use strict';

  /**
   * Marked Highlight
   * A plugin for the marked library that adds syntax highlighting to code blocks.
   */
  function markedHighlight(options) {
    if (typeof options === 'function') {
      options = {
        highlight: options
      };
    }

    if (!options || typeof options.highlight !== 'function') {
      throw new Error('Must provide a highlight function');
    }

    const highlight = options.highlight;
    const langPrefix = options.langPrefix || 'language-';

    return {
      renderer: {
        code(code, language) {
          try {
            // Ensure code is a string
            if (typeof code !== 'string') {
              if (code && typeof code === 'object') {
                // Try to extract text from token object
                code = code.text || code.raw || code.value || String(code);
              } else {
                code = String(code || '');
              }
            }
            
            const highlighted = highlight(code, language || 'plaintext');
            const classAttr = language ? ` class="${langPrefix}${language}"` : '';
            
            if (highlighted && highlighted.value) {
              return `<div class="code-container">
                <button class="copy-btn">Copy</button>
                <pre><code${classAttr}>${highlighted.value}</code></pre>
              </div>`;
            }
            
            return `<div class="code-container">
              <button class="copy-btn">Copy</button>
              <pre><code${classAttr}>${code}</code></pre>
            </div>`;
          } catch (error) {
            console.error('Error in marked-highlight code renderer:', error);
            return `<div class="code-container">
              <button class="copy-btn">Copy</button>
              <pre><code class="${langPrefix}plaintext">${String(code || '')}</code></pre>
            </div>`;
          }
        }
      }
    };
  }

  return markedHighlight;
}));
