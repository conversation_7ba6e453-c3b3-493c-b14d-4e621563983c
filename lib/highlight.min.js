/*! highlight.js v11.9.0 | BSD3 License | git.io/hljslicense */
var hljs = (function() {
  function escape(value) {
    // Ensure value is a string before trying to replace
    if (typeof value !== 'string') {
      console.warn('highlight.js: Expected string but got', typeof value);
      value = String(value || '');
    }
    return value.replace(/[&<>]/gm, function(ch) {
      return {'&': '&amp;', '<': '&lt;', '>': '&gt;'}[ch];
    });
  }

  return {
    getLanguage: function(lang) {
      return true; // Simplified - always say we support the language
    },
    highlight: function(code, language) {
      try {
        // Handle non-string inputs
        if (typeof code !== 'string') {
          console.warn('highlight.js: Expected string but got', typeof code);
          if (code && typeof code === 'object') {
            // Try to extract text from token object
            code = code.text || code.raw || code.value || String(code);
          } else {
            code = String(code || '');
          }
        }
        
        // Simple highlighting - just escape HTML
        return {
          value: escape(code),
          language: language || 'plaintext',
          relevance: 1,
          illegal: false
        };
      } catch(e) {
        console.error('Highlight error:', e);
        return {
          value: typeof code === 'string' ? code : String(code || ''),
          language: 'plaintext',
          relevance: 0,
          illegal: true
        };
      }
    }
  };
})();

// Make hljs available globally
window.hljs = hljs;
