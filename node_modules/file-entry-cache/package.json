{"name": "file-entry-cache", "version": "10.1.0", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/file-entry-cache"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": false, "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "devDependencies": {"@types/node": "^22.15.8", "@vitest/coverage-v8": "^3.1.3", "rimraf": "^6.0.1", "tsup": "^8.4.0", "typescript": "^5.8.3", "vitest": "^3.1.3", "xo": "^0.60.0"}, "dependencies": {"flat-cache": "^6.1.9"}, "files": ["dist", "license"], "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "prepublish": "pnpm build", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest run", "clean": "rimraf ./dist ./coverage ./node_modules"}}