// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const valueParser = require('postcss-value-parser');
const nodeFieldIndices = require('../../utils/nodeFieldIndices.cjs');
const regexes = require('../../utils/regexes.cjs');
const getAtRuleParams = require('../../utils/getAtRuleParams.cjs');
const report = require('../../utils/report.cjs');
const ruleMessages = require('../../utils/ruleMessages.cjs');
const setAtRuleParams = require('../../utils/setAtRuleParams.cjs');
const validateOptions = require('../../utils/validateOptions.cjs');

const ruleName = 'import-notation';

const messages = ruleMessages(ruleName, {
	expected: (unfixed, fixed) => `Expected "${unfixed}" to be "${fixed}"`,
});

const meta = {
	url: 'https://stylelint.io/user-guide/rules/import-notation',
	fixable: true,
};

/** @typedef {import('postcss').AtRule} AtRule */

/**
 * @param {AtRule} node
 * @param {string} fixed
 * @param {number} index
 */
const fixer = (node, fixed, index) => () => {
	const restAtRuleParams = node.params.slice(index);

	setAtRuleParams(node, `${fixed}${restAtRuleParams}`);
};

/** @type {import('stylelint').CoreRules[ruleName]} */
const rule = (primary) => {
	return (root, result) => {
		const validOptions = validateOptions(result, ruleName, {
			actual: primary,
			possible: ['string', 'url'],
		});

		if (!validOptions) return;

		root.walkAtRules(regexes.atRuleRegexes.importName, checkAtRuleImportParams);

		/** @param {AtRule} atRule */
		function checkAtRuleImportParams(atRule) {
			const params = getAtRuleParams(atRule);
			const index = nodeFieldIndices.atRuleParamIndex(atRule);
			const parsed = valueParser(params);

			for (const node of parsed.nodes) {
				const { sourceEndIndex, type, value } = node;
				const endIndex = index + sourceEndIndex;
				const problem = { node: atRule, index, endIndex, result, ruleName };

				if (primary === 'string') {
					if (type !== 'function' || value.toLowerCase() !== 'url') continue;

					const urlFunctionFull = valueParser.stringify(node);
					const urlFunctionArguments = valueParser.stringify(node.nodes);
					const quotedUrlFunctionFirstArgument =
						node.nodes[0]?.type === 'word' ? `"${urlFunctionArguments}"` : urlFunctionArguments;
					const fix = fixer(atRule, quotedUrlFunctionFirstArgument, sourceEndIndex);
					const message = messages.expected;
					const messageArgs = [urlFunctionFull, quotedUrlFunctionFirstArgument];

					report({ ...problem, message, messageArgs, fix: { apply: fix, node: problem.node } });

					return;
				}

				if (primary === 'url') {
					if (type === 'space') return;

					if (type !== 'word' && type !== 'string') continue;

					const path = valueParser.stringify(node);
					const urlFunctionFull = `url(${path})`;
					const quotedNodeValue =
						type === 'word' ? `"${value}"` : `${node.quote}${value}${node.quote}`;
					const fix = fixer(atRule, urlFunctionFull, sourceEndIndex);
					const message = messages.expected;
					const messageArgs = [quotedNodeValue, urlFunctionFull];

					report({ ...problem, message, messageArgs, fix: { apply: fix, node: problem.node } });

					return;
				}
			}
		}
	};
};

rule.ruleName = ruleName;
rule.messages = messages;
rule.meta = meta;

module.exports = rule;
