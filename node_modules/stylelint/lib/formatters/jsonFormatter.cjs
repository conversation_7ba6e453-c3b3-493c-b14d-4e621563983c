// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

/**
 * Omit any properties starting with `_`, which are fake-private
 *
 * @type {import('stylelint').Formatter}
 */
function jsonFormatter(results) {
	return JSON.stringify(results, (key, value) => {
		return key[0] === '_' ? undefined : value;
	});
}

module.exports = jsonFormatter;
