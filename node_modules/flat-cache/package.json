{"name": "flat-cache", "version": "6.1.9", "description": "A simple key/value storage using files to persist the data", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/flat-cache"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": false, "keywords": ["cache", "caching", "cacheable", "flat-cache", "flat", "file", "file-cache", "file-caching", "file-based-cache", "file-persist", "file-persistence", "file-storage", "file-system", "file-management", "filesystem-cache", "disk-cache", "cache-persistence", "cache-persist", "persistent-cache", "persistent-storage", "cache-to-file", "cache-on-disk", "cache-file", "cache-expiration", "cache-lifetime", "data-persistence", "data-storage", "local-storage", "file-system-cache"], "devDependencies": {"@types/node": "^22.15.8", "@vitest/coverage-v8": "^3.1.3", "rimraf": "^6.0.1", "tsup": "^8.4.0", "typescript": "^5.8.3", "vitest": "^3.1.3", "xo": "^0.60.0"}, "dependencies": {"flatted": "^3.3.3", "hookified": "^1.8.2", "cacheable": "^1.9.0"}, "files": ["dist", "license"], "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "prepublish": "pnpm build", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest run", "clean": "rimraf ./dist ./coverage ./node_modules"}}