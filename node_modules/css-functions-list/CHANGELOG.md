# Changelog

## [Unreleased][]

## [3.2.3][] - 2024-10-09

-   Add `calc-size` and `shape` function
-   Filter invalid functions

## [3.2.2][] - 2024-04-22

-   Add additional function references

## [3.2.1][] - 2023-10-15

### Changed

-   Add additional function references

## [3.2.0][] - 2023-07-10

### Changed

-   Update with latest function definitions
-   Keep functions that end with `X|Y|Z`, like `rotatex` or `rotatey` (`rotateX`
    or `rotateY`) ([#4](https://github.com/niksy/css-functions-list/pull/5))

## [3.1.0][] - 2022-06-03

### Added

-   Add OKLCH and OKLab functions
    ([#4](https://github.com/niksy/css-functions-list/pull/4))

## [3.0.1][] - 2022-02-14

### Fixed

-   Use `fileURLToPath` for location resolve

### Added

-   Windows to CI matrix

## [3.0.0][] - 2022-02-03

### Changed

-   **Breaking**: Only Node >= 12.22 is supported

### Added

-   New additions to list

## [2.0.0][] - 2021-09-02

### Added

-   Initial implementation

<!-- prettier-ignore-start -->

[2.0.0]: https://github.com/niksy/css-functions-list/tree/v2.0.0
[3.0.0]: https://github.com/niksy/css-functions-list/tree/v3.0.0
[3.0.1]: https://github.com/niksy/css-functions-list/tree/v3.0.1
[3.1.0]: https://github.com/niksy/css-functions-list/tree/v3.1.0
[Unreleased]: https://github.com/niksy/css-functions-list/compare/v3.2.3...HEAD
[3.2.3]: https://github.com/niksy/css-functions-list/compare/v3.2.2...v3.2.3
[3.2.2]: https://github.com/niksy/css-functions-list/compare/v3.2.1...v3.2.2
[3.2.1]: https://github.com/niksy/css-functions-list/compare/v3.2.0...v3.2.1
[3.2.0]: https://github.com/niksy/css-functions-list/tree/v3.2.0
