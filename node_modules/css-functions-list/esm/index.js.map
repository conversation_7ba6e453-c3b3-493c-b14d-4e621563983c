{"version": 3, "file": "index.js", "sources": ["../index.js"], "sourcesContent": ["import { URL, fileURLToPath } from 'url';\n\n/**\n * Path to CSS functions list JSON file.\n */\nconst location = fileURLToPath(new URL('index.json', import.meta.url));\n\nexport default location;\n"], "names": [], "mappings": ";;AAEA;AACA;AACA;AACK,MAAC,QAAQ,GAAG,aAAa,CAAC,IAAI,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;;;;"}