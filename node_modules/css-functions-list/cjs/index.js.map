{"version": 3, "file": "index.js", "sources": ["../index.js"], "sourcesContent": ["import { URL, fileURLToPath } from 'url';\n\n/**\n * Path to CSS functions list JSON file.\n */\nconst location = fileURLToPath(new URL('index.json', import.meta.url));\n\nexport default location;\n"], "names": ["fileURLToPath", "URL"], "mappings": ";;;;AAEA;AACA;AACA;AACK,MAAC,QAAQ,GAAGA,iBAAa,CAAC,IAAIC,OAAG,CAAC,YAAY,EAAE,mMAAe,CAAC;;;;"}