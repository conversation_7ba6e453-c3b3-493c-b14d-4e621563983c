{"name": "mathml-tag-names", "version": "2.1.3", "description": "List of known MathML tag-names", "license": "MIT", "keywords": ["MathML", "math", "tag", "name", "element", "tagname", "w3c"], "repository": "wooorm/mathml-tag-names", "bugs": "https://github.com/wooorm/mathml-tag-names/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "main": "index.json", "files": ["index.json"], "dependencies": {}, "devDependencies": {"bail": "^1.0.0", "browserify": "^16.0.0", "concat-stream": "^2.0.0", "hast-util-select": "^3.0.0", "hast-util-to-string": "^1.0.0", "prettier": "^1.0.0", "rehype-parse": "^6.0.0", "remark-cli": "^7.0.0", "remark-preset-wooorm": "^6.0.0", "tape": "^4.0.0", "tinyify": "^2.0.0", "unified": "^8.0.0", "xo": "^0.25.0"}, "scripts": {"generate": "node build", "format": "remark . -qfo && prettier --write \"**/*.js\" && xo --fix", "build-bundle": "browserify index.json -s mathML -o mathml-tag-names.js", "build-mangle": "browserify index.json -s mathML -p tinyify -o mathml-tag-names.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test": "npm run generate && npm run format && npm run build && npm run test-api"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignore": ["mathml-tag-names.js"]}, "remarkConfig": {"plugins": ["preset-wooorm"]}}