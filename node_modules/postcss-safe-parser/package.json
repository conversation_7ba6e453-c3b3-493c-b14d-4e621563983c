{"name": "postcss-safe-parser", "version": "7.0.1", "description": "Fault-tolerant CSS parser for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "parser", "fault tolerant"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "postcss/postcss-safe-parser", "engines": {"node": ">=18.0"}, "main": "lib/safe-parse", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss-safe-parser"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "peerDependencies": {"postcss": "^8.4.31"}}