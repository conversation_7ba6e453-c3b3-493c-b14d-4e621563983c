# PostCSS Safe Parser

<img align="right" width="135" height="95"
     title="Philoso<PERSON>’s stone, logo of PostCSS"
     src="https://postcss.org/logo-leftp.svg">

A fault-tolerant CSS parser for [PostCSS], which will find & fix syntax errors,
capable of parsing any input. It is useful for:

* Parse legacy code with many hacks. For example, it can parse all examples
  from [Browserhacks].
* Works with demo tools with live input like [Autoprefixer demo].

[Autoprefixer demo]: http://simevidas.jsbin.com/gufoko/quiet
[Browserhacks]:      http://browserhacks.com/
[PostCSS]:           https://github.com/postcss/postcss

<a href="https://evilmartians.com/?utm_source=postcss">
  <img src="https://evilmartians.com/badges/sponsored-by-evil-martians.svg"
    alt="Sponsored by Evil Martians" width="236" height="54">
</a>


## Docs
Read full docs **[here](https://github.com/postcss/postcss-safe-parser#readme)**.
