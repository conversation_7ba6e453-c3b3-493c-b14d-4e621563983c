{"name": "cacheable", "version": "1.9.0", "description": "High Performance Layer 1 / Layer 2 Caching with Keyv Storage", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/cacheable"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": false, "devDependencies": {"@faker-js/faker": "^9.7.0", "@keyv/redis": "^4.4.0", "@types/node": "^22.15.3", "@vitest/coverage-v8": "^3.1.3", "lru-cache": "^11.1.0", "rimraf": "^6.0.1", "tsup": "^8.4.0", "typescript": "^5.8.3", "vitest": "^3.1.3", "xo": "^0.60.0"}, "dependencies": {"hookified": "^1.8.2", "keyv": "^5.3.3"}, "keywords": ["cacheable", "high performance", "layer 1 caching", "layer 2 caching", "distributed caching", "Keyv storage engine", "memory caching", "LRU cache", "expiration", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offline support", "distributed sync", "secondary store", "primary store", "non-blocking operations", "cache statistics", "layered caching", "fault tolerant", "scalable cache", "in-memory cache", "distributed cache", "lruSize", "lru", "multi-tier cache"], "files": ["dist", "LICENSE"], "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "prepublish": "pnpm build", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest run", "clean": "rimraf ./dist ./coverage ./node_modules"}}