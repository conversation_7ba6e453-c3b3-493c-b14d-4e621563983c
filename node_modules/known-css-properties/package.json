{"name": "known-css-properties", "version": "0.36.0", "description": "List of known CSS properties", "repository": "known-css/known-css-properties", "main": "index.js", "scripts": {"bump": "npm version patch && npm publish && git push && git push --tags", "download-w3c-data": "node scripts/download-w3c-data.mjs", "generate": "node scripts/generate.mjs", "lint": "eslint index.js scripts/*.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["css", "properties", "w3c", "chrome", "firefox", "explorer", "edge", "safari", "opera", "samsung-internet", "uc-browser"], "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://kbebenek.me"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://beanon.com"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://beanon.com"}, "license": "MIT", "bugs": {"url": "https://github.com/known-css/known-css-properties/issues"}, "homepage": "https://github.com/known-css/known-css-properties#readme", "devDependencies": {"eslint": "8.57.0", "globby": "14.0.1", "lodash.sortby": "4.7.0", "lodash.uniq": "4.5.0"}}