{"version": 3, "sources": ["../../src/eventified.ts", "../../src/index.ts"], "sourcesContent": ["import {type Logger} from 'logger.js';\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport type IEventEmitter = {\n\t/**\n\t * Registers a listener for the specified event.\n\t *\n\t * @param eventName - The name (or symbol) of the event to listen for.\n\t * @param listener - A callback function that will be invoked when the event is emitted.\n\t * @returns The current instance of EventEmitter for method chaining.\n\t *\n\t * @example\n\t * emitter.on('data', (message) => {\n\t *   console.log(message);\n\t * });\n\t */\n\ton(eventName: string | symbol, listener: (...arguments_: any[]) => void): IEventEmitter;\n\n\t/**\n\t * Alias for `on`. Registers a listener for the specified event.\n\t *\n\t * @param eventName - The name (or symbol) of the event to listen for.\n\t * @param listener - A callback function that will be invoked when the event is emitted.\n\t * @returns The current instance of EventEmitter for method chaining.\n\t */\n\taddListener(eventName: string | symbol, listener: (...arguments_: any[]) => void): IEventEmitter;\n\n\t/**\n\t * Registers a one-time listener for the specified event. The listener is removed after it is called once.\n\t *\n\t * @param eventName - The name (or symbol) of the event to listen for.\n\t * @param listener - A callback function that will be invoked once when the event is emitted.\n\t * @returns The current instance of EventEmitter for method chaining.\n\t *\n\t * @example\n\t * emitter.once('close', () => {\n\t *   console.log('The connection was closed.');\n\t * });\n\t */\n\tonce(eventName: string | symbol, listener: (...arguments_: any[]) => void): IEventEmitter;\n\n\t/**\n\t * Removes a previously registered listener for the specified event.\n\t *\n\t * @param eventName - The name (or symbol) of the event to stop listening for.\n\t * @param listener - The specific callback function to remove.\n\t * @returns The current instance of EventEmitter for method chaining.\n\t *\n\t * @example\n\t * emitter.off('data', myListener);\n\t */\n\toff(eventName: string | symbol, listener: (...arguments_: any[]) => void): IEventEmitter;\n\n\t/**\n\t * Alias for `off`. Removes a previously registered listener for the specified event.\n\t *\n\t * @param eventName - The name (or symbol) of the event to stop listening for.\n\t * @param listener - The specific callback function to remove.\n\t * @returns The current instance of EventEmitter for method chaining.\n\t */\n\tremoveListener(eventName: string | symbol, listener: (...arguments_: any[]) => void): IEventEmitter;\n\n\t/**\n\t * Emits the specified event, invoking all registered listeners with the provided arguments.\n\t *\n\t * @param eventName - The name (or symbol) of the event to emit.\n\t * @param args - Arguments passed to each listener.\n\t * @returns `true` if the event had listeners, `false` otherwise.\n\t *\n\t * @example\n\t * emitter.emit('data', 'Hello World');\n\t */\n\temit(eventName: string | symbol, ...arguments_: any[]): boolean;\n\n\t/**\n\t * Returns the number of listeners registered for the specified event.\n\t *\n\t * @param eventName - The name (or symbol) of the event.\n\t * @returns The number of registered listeners.\n\t *\n\t * @example\n\t * const count = emitter.listenerCount('data');\n\t * console.log(count); // e.g., 2\n\t */\n\tlistenerCount(eventName: string | symbol): number;\n\n\t/**\n\t * Removes all listeners for the specified event. If no event is specified, it removes all listeners for all events.\n\t *\n\t * @param eventName - (Optional) The name (or symbol) of the event.\n\t * @returns The current instance of EventEmitter for method chaining.\n\t *\n\t * @example\n\t * emitter.removeAllListeners('data');\n\t */\n\tremoveAllListeners(eventName?: string | symbol): IEventEmitter;\n\n\t/**\n\t * Returns an array of event names for which listeners have been registered.\n\t *\n\t * @returns An array of event names (or symbols).\n\t *\n\t * @example\n\t * const events = emitter.eventNames();\n\t * console.log(events); // e.g., ['data', 'close']\n\t */\n\teventNames(): Array<string | symbol>;\n\n\t/**\n\t * Returns an array of listeners registered for the specified event.\n\t *\n\t * @param eventName - The name (or symbol) of the event.\n\t * @returns An array of listener functions.\n\t *\n\t * @example\n\t * const listeners = emitter.listeners('data');\n\t * console.log(listeners.length); // e.g., 2\n\t */\n\tlisteners(eventName: string | symbol): Array<(...arguments_: any[]) => void>;\n\n\t/**\n\t * Returns an array of raw listeners for the specified event. This includes listeners wrapped by internal mechanisms (e.g., once-only listeners).\n\t *\n\t * @param eventName - The name (or symbol) of the event.\n\t * @returns An array of raw listener functions.\n\t *\n\t * @example\n\t * const rawListeners = emitter.rawListeners('data');\n\t */\n\trawListeners(eventName: string | symbol): Array<(...arguments_: any[]) => void>;\n\n\t/**\n\t * Adds a listener to the beginning of the listeners array for the specified event.\n\t *\n\t * @param eventName - The name (or symbol) of the event to listen for.\n\t * @param listener - A callback function that will be invoked when the event is emitted.\n\t * @returns The current instance of EventEmitter for method chaining.\n\t *\n\t * @example\n\t * emitter.prependListener('data', (message) => {\n\t *   console.log('This will run first.');\n\t * });\n\t */\n\tprependListener(eventName: string | symbol, listener: (...arguments_: any[]) => void): IEventEmitter;\n\n\t/**\n\t * Adds a one-time listener to the beginning of the listeners array for the specified event.\n\t *\n\t * @param eventName - The name (or symbol) of the event to listen for.\n\t * @param listener - A callback function that will be invoked once when the event is emitted.\n\t * @returns The current instance of EventEmitter for method chaining.\n\t *\n\t * @example\n\t * emitter.prependOnceListener('data', (message) => {\n\t *   console.log('This will run first and only once.');\n\t * });\n\t */\n\tprependOnceListener(eventName: string | symbol, listener: (...arguments_: any[]) => void): IEventEmitter;\n};\n\nexport type EventListener = (...arguments_: any[]) => void;\n\nexport type EventEmitterOptions = {\n\tlogger?: Logger;\n};\n\nexport class Eventified implements IEventEmitter {\n\t_eventListeners: Map<string | symbol, EventListener[]>;\n\t_maxListeners: number;\n\t_logger?: Logger;\n\n\tconstructor(options?: EventEmitterOptions) {\n\t\tthis._eventListeners = new Map<string | symbol, EventListener[]>();\n\t\tthis._maxListeners = 100; // Default maximum number of listeners\n\t\tthis._logger = options?.logger;\n\t}\n\n\t/**\n\t * Adds a handler function for a specific event that will run only once\n\t * @param {string | symbol} eventName\n\t * @param {EventListener} listener\n\t * @returns {IEventEmitter} returns the instance of the class for chaining\n\t */\n\tpublic once(eventName: string | symbol, listener: EventListener): IEventEmitter {\n\t\tconst onceListener: EventListener = (...arguments_: any[]) => {\n\t\t\tthis.off(eventName as string, onceListener);\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n\t\t\tlistener(...arguments_);\n\t\t};\n\n\t\tthis.on(eventName as string, onceListener);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Gets the number of listeners for a specific event. If no event is provided, it returns the total number of listeners\n\t * @param {string} eventName The event name. Not required\n\t * @returns {number} The number of listeners\n\t */\n\tpublic listenerCount(eventName?: string | symbol): number {\n\t\tif (!eventName) {\n\t\t\treturn this.getAllListeners().length;\n\t\t}\n\n\t\tconst listeners = this._eventListeners.get(eventName as string);\n\t\treturn listeners ? listeners.length : 0;\n\t}\n\n\t/**\n\t * Gets an array of event names\n\t * @returns {Array<string | symbol>} An array of event names\n\t */\n\tpublic eventNames(): Array<string | symbol> {\n\t\treturn Array.from(this._eventListeners.keys());\n\t}\n\n\t/**\n\t * Gets an array of listeners for a specific event. If no event is provided, it returns all listeners\n\t * @param {string} [event] (Optional) The event name\n\t * @returns {EventListener[]} An array of listeners\n\t */\n\tpublic rawListeners(event?: string | symbol): EventListener[] {\n\t\tif (!event) {\n\t\t\treturn this.getAllListeners();\n\t\t}\n\n\t\treturn this._eventListeners.get(event) ?? [];\n\t}\n\n\t/**\n\t * Prepends a listener to the beginning of the listeners array for the specified event\n\t * @param {string | symbol} eventName\n\t * @param {EventListener} listener\n\t * @returns {IEventEmitter} returns the instance of the class for chaining\n\t */\n\tpublic prependListener(eventName: string | symbol, listener: EventListener): IEventEmitter {\n\t\tconst listeners = this._eventListeners.get(eventName) ?? [];\n\t\tlisteners.unshift(listener);\n\t\tthis._eventListeners.set(eventName, listeners);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Prepends a one-time listener to the beginning of the listeners array for the specified event\n\t * @param {string | symbol} eventName\n\t * @param {EventListener} listener\n\t * @returns {IEventEmitter} returns the instance of the class for chaining\n\t */\n\tpublic prependOnceListener(eventName: string | symbol, listener: EventListener): IEventEmitter {\n\t\tconst onceListener: EventListener = (...arguments_: any[]) => {\n\t\t\tthis.off(eventName as string, onceListener);\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n\t\t\tlistener(...arguments_);\n\t\t};\n\n\t\tthis.prependListener(eventName as string, onceListener);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Gets the maximum number of listeners that can be added for a single event\n\t * @returns {number} The maximum number of listeners\n\t */\n\tpublic maxListeners(): number {\n\t\treturn this._maxListeners;\n\t}\n\n\t/**\n\t * Adds a listener for a specific event. It is an alias for the on() method\n\t * @param {string | symbol} event\n\t * @param {EventListener} listener\n\t * @returns {IEventEmitter} returns the instance of the class for chaining\n\t */\n\tpublic addListener(event: string | symbol, listener: EventListener): IEventEmitter {\n\t\tthis.on(event, listener);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a listener for a specific event\n\t * @param {string | symbol} event\n\t * @param {EventListener} listener\n\t * @returns {IEventEmitter} returns the instance of the class for chaining\n\t */\n\tpublic on(event: string | symbol, listener: EventListener): IEventEmitter {\n\t\tif (!this._eventListeners.has(event)) {\n\t\t\tthis._eventListeners.set(event, []);\n\t\t}\n\n\t\tconst listeners = this._eventListeners.get(event);\n\n\t\tif (listeners) {\n\t\t\tif (listeners.length >= this._maxListeners) {\n\t\t\t\tconsole.warn(`MaxListenersExceededWarning: Possible event memory leak detected. ${listeners.length + 1} ${event as string} listeners added. Use setMaxListeners() to increase limit.`);\n\t\t\t}\n\n\t\t\tlisteners.push(listener);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Removes a listener for a specific event. It is an alias for the off() method\n\t * @param {string | symbol} event\n\t * @param {EventListener} listener\n\t * @returns {IEventEmitter} returns the instance of the class for chaining\n\t */\n\tpublic removeListener(event: string, listener: EventListener): IEventEmitter {\n\t\tthis.off(event, listener);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Removes a listener for a specific event\n\t * @param {string | symbol} event\n\t * @param {EventListener} listener\n\t * @returns {IEventEmitter} returns the instance of the class for chaining\n\t */\n\tpublic off(event: string | symbol, listener: EventListener): IEventEmitter {\n\t\tconst listeners = this._eventListeners.get(event) ?? [];\n\t\tconst index = listeners.indexOf(listener);\n\t\tif (index !== -1) {\n\t\t\tlisteners.splice(index, 1);\n\t\t}\n\n\t\tif (listeners.length === 0) {\n\t\t\tthis._eventListeners.delete(event);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Calls all listeners for a specific event\n\t * @param {string | symbol} event\n\t * @param arguments_ The arguments to pass to the listeners\n\t * @returns {boolean} Returns true if the event had listeners, false otherwise\n\t */\n\tpublic emit(event: string | symbol, ...arguments_: any[]): boolean {\n\t\tlet result = false;\n\t\tconst listeners = this._eventListeners.get(event);\n\n\t\tif (listeners && listeners.length > 0) {\n\t\t\tfor (const listener of listeners) {\n\t\t\t\t// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n\t\t\t\tlistener(...arguments_);\n\t\t\t\tresult = true;\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t/**\n\t * Gets all listeners for a specific event. If no event is provided, it returns all listeners\n\t * @param {string} [event] (Optional) The event name\n\t * @returns {EventListener[]} An array of listeners\n\t */\n\tpublic listeners(event: string): EventListener[] {\n\t\treturn this._eventListeners.get(event) ?? [];\n\t}\n\n\t/**\n\t * Removes all listeners for a specific event. If no event is provided, it removes all listeners\n\t * @param {string} [event] (Optional) The event name\n\t * @returns {IEventEmitter} returns the instance of the class for chaining\n\t */\n\tpublic removeAllListeners(event?: string): IEventEmitter {\n\t\tif (event) {\n\t\t\tthis._eventListeners.delete(event);\n\t\t} else {\n\t\t\tthis._eventListeners.clear();\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the maximum number of listeners that can be added for a single event\n\t * @param {number} n The maximum number of listeners\n\t * @returns {void}\n\t */\n\tpublic setMaxListeners(n: number): void {\n\t\tthis._maxListeners = n;\n\t\tfor (const listeners of this._eventListeners.values()) {\n\t\t\tif (listeners.length > n) {\n\t\t\t\tlisteners.splice(n);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Gets all listeners\n\t * @returns {EventListener[]} An array of listeners\n\t */\n\tpublic getAllListeners(): EventListener[] {\n\t\tlet result = new Array<EventListener>();\n\t\tfor (const listeners of this._eventListeners.values()) {\n\t\t\tresult = result.concat(listeners);\n\t\t}\n\n\t\treturn result;\n\t}\n}\n", "import {Eventified} from './eventified.js';\nimport {type Logger} from './logger.js';\n\nexport type Hook = (...arguments_: any[]) => Promise<void> | void;\n\nexport type HookEntry = {\n\tevent: string;\n\thandler: Hook;\n};\n\nexport type HookifiedOptions = {\n\tthrowHookErrors?: boolean;\n\tlogger?: Logger;\n};\n\nexport class Hookified extends Eventified {\n\t_hooks: Map<string, Hook[]>;\n\t_throwHookErrors = false;\n\n\tconstructor(options?: HookifiedOptions) {\n\t\tsuper({logger: options?.logger});\n\t\tthis._hooks = new Map();\n\n\t\tif (options?.throwHookErrors !== undefined) {\n\t\t\tthis._throwHookErrors = options.throwHookErrors;\n\t\t}\n\t}\n\n\t/**\n\t * Gets all hooks\n\t * @returns {Map<string, Hook[]>}\n\t */\n\tpublic get hooks() {\n\t\treturn this._hooks;\n\t}\n\n\t/**\n\t * Gets whether an error should be thrown when a hook throws an error. Default is false and only emits an error event.\n\t * @returns {boolean}\n\t */\n\tpublic get throwHookErrors() {\n\t\treturn this._throwHookErrors;\n\t}\n\n\t/**\n\t * Sets whether an error should be thrown when a hook throws an error. Default is false and only emits an error event.\n\t * @param {boolean} value\n\t */\n\tpublic set throwHookErrors(value) {\n\t\tthis._throwHookErrors = value;\n\t}\n\n\t/**\n\t * Gets the logger\n\t * @returns {Logger}\n\t */\n\tpublic get logger(): Logger | undefined {\n\t\treturn this._logger;\n\t}\n\n\t/**\n\t * Sets the logger\n\t * @param {Logger} logger\n\t */\n\tpublic set logger(logger: Logger | undefined) {\n\t\tthis._logger = logger;\n\t}\n\n\t/**\n\t * Adds a handler function for a specific event\n\t * @param {string} event\n\t * @param {Hook} handler - this can be async or sync\n\t * @returns {void}\n\t */\n\tpublic onHook(event: string, handler: Hook) {\n\t\tconst eventHandlers = this._hooks.get(event);\n\t\tif (eventHandlers) {\n\t\t\teventHandlers.push(handler);\n\t\t} else {\n\t\t\tthis._hooks.set(event, [handler]);\n\t\t}\n\t}\n\n\t/**\n\t * Adds a handler function for a specific event\n\t * @param {Array<HookEntry>} hooks\n\t * @returns {void}\n\t */\n\tpublic onHooks(hooks: HookEntry[]) {\n\t\tfor (const hook of hooks) {\n\t\t\tthis.onHook(hook.event, hook.handler);\n\t\t}\n\t}\n\n\t/**\n\t * Adds a handler function for a specific event that runs before all other handlers\n\t * @param {string} event\n\t * @param {Hook} handler - this can be async or sync\n\t * @returns {void}\n\t */\n\tpublic prependHook(event: string, handler: Hook) {\n\t\tconst eventHandlers = this._hooks.get(event);\n\t\tif (eventHandlers) {\n\t\t\teventHandlers.unshift(handler);\n\t\t} else {\n\t\t\tthis._hooks.set(event, [handler]);\n\t\t}\n\t}\n\n\t/**\n\t * Adds a handler that only executes once for a specific event before all other handlers\n\t * @param event\n\t * @param handler\n\t */\n\tpublic prependOnceHook(event: string, handler: Hook) {\n\t\tconst hook = async (...arguments_: any[]) => {\n\t\t\tthis.removeHook(event, hook);\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n\t\t\treturn handler(...arguments_);\n\t\t};\n\n\t\tthis.prependHook(event, hook);\n\t}\n\n\t/**\n\t * Adds a handler that only executes once for a specific event\n\t * @param event\n\t * @param handler\n\t */\n\tpublic onceHook(event: string, handler: Hook) {\n\t\tconst hook = async (...arguments_: any[]) => {\n\t\t\tthis.removeHook(event, hook);\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n\t\t\treturn handler(...arguments_);\n\t\t};\n\n\t\tthis.onHook(event, hook);\n\t}\n\n\t/**\n\t * Removes a handler function for a specific event\n\t * @param {string} event\n\t * @param {Hook} handler\n\t * @returns {void}\n\t */\n\tpublic removeHook(event: string, handler: Hook) {\n\t\tconst eventHandlers = this._hooks.get(event);\n\t\tif (eventHandlers) {\n\t\t\tconst index = eventHandlers.indexOf(handler);\n\t\t\tif (index !== -1) {\n\t\t\t\teventHandlers.splice(index, 1);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Removes all handlers for a specific event\n\t * @param {Array<HookEntry>} hooks\n\t * @returns {void}\n\t */\n\tpublic removeHooks(hooks: HookEntry[]) {\n\t\tfor (const hook of hooks) {\n\t\t\tthis.removeHook(hook.event, hook.handler);\n\t\t}\n\t}\n\n\t/**\n\t * Calls all handlers for a specific event\n\t * @param {string} event\n\t * @param {T[]} arguments_\n\t * @returns {Promise<void>}\n\t */\n\tpublic async hook<T>(event: string, ...arguments_: T[]) {\n\t\tconst eventHandlers = this._hooks.get(event);\n\t\tif (eventHandlers) {\n\t\t\tfor (const handler of eventHandlers) {\n\t\t\t\ttry {\n\t\t\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\t\t\tawait handler(...arguments_);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconst message = `${event}: ${(error as Error).message}`;\n\t\t\t\t\tthis.emit('error', new Error(message));\n\t\t\t\t\tif (this._logger) {\n\t\t\t\t\t\tthis._logger.error(message);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (this._throwHookErrors) {\n\t\t\t\t\t\tthrow new Error(message);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Gets all hooks for a specific event\n\t * @param {string} event\n\t * @returns {Hook[]}\n\t */\n\tpublic getHooks(event: string) {\n\t\treturn this._hooks.get(event);\n\t}\n\n\t/**\n\t * Removes all hooks\n\t * @returns {void}\n\t */\n\tpublic clearHooks() {\n\t\tthis._hooks.clear();\n\t}\n}\n\nexport {Eventified, type EventListener} from './eventified.js';\nexport {type Logger} from './logger.js';\n"], "mappings": "oKAsKO,IAAMA,EAAN,KAA0C,CAKhD,YAAYC,EAA+B,CAJ3CC,EAAA,wBACAA,EAAA,sBACAA,EAAA,gBAGC,KAAK,gBAAkB,IAAI,IAC3B,KAAK,cAAgB,IACrB,KAAK,QAAUD,GAAS,MACzB,CAQO,KAAKE,EAA4BC,EAAwC,CAC/E,IAAMC,EAA8B,IAAIC,IAAsB,CAC7D,KAAK,IAAIH,EAAqBE,CAAY,EAE1CD,EAAS,GAAGE,CAAU,CACvB,EAEA,YAAK,GAAGH,EAAqBE,CAAY,EAClC,IACR,CAOO,cAAcF,EAAqC,CACzD,GAAI,CAACA,EACJ,OAAO,KAAK,gBAAgB,EAAE,OAG/B,IAAMI,EAAY,KAAK,gBAAgB,IAAIJ,CAAmB,EAC9D,OAAOI,EAAYA,EAAU,OAAS,CACvC,CAMO,YAAqC,CAC3C,OAAO,MAAM,KAAK,KAAK,gBAAgB,KAAK,CAAC,CAC9C,CAOO,aAAaC,EAA0C,CAC7D,OAAKA,EAIE,KAAK,gBAAgB,IAAIA,CAAK,GAAK,CAAC,EAHnC,KAAK,gBAAgB,CAI9B,CAQO,gBAAgBL,EAA4BC,EAAwC,CAC1F,IAAMG,EAAY,KAAK,gBAAgB,IAAIJ,CAAS,GAAK,CAAC,EAC1D,OAAAI,EAAU,QAAQH,CAAQ,EAC1B,KAAK,gBAAgB,IAAID,EAAWI,CAAS,EACtC,IACR,CAQO,oBAAoBJ,EAA4BC,EAAwC,CAC9F,IAAMC,EAA8B,IAAIC,IAAsB,CAC7D,KAAK,IAAIH,EAAqBE,CAAY,EAE1CD,EAAS,GAAGE,CAAU,CACvB,EAEA,YAAK,gBAAgBH,EAAqBE,CAAY,EAC/C,IACR,CAMO,cAAuB,CAC7B,OAAO,KAAK,aACb,CAQO,YAAYG,EAAwBJ,EAAwC,CAClF,YAAK,GAAGI,EAAOJ,CAAQ,EAChB,IACR,CAQO,GAAGI,EAAwBJ,EAAwC,CACpE,KAAK,gBAAgB,IAAII,CAAK,GAClC,KAAK,gBAAgB,IAAIA,EAAO,CAAC,CAAC,EAGnC,IAAMD,EAAY,KAAK,gBAAgB,IAAIC,CAAK,EAEhD,OAAID,IACCA,EAAU,QAAU,KAAK,eAC5B,QAAQ,KAAK,qEAAqEA,EAAU,OAAS,CAAC,IAAIC,CAAe,4DAA4D,EAGtLD,EAAU,KAAKH,CAAQ,GAGjB,IACR,CAQO,eAAeI,EAAeJ,EAAwC,CAC5E,YAAK,IAAII,EAAOJ,CAAQ,EACjB,IACR,CAQO,IAAII,EAAwBJ,EAAwC,CAC1E,IAAMG,EAAY,KAAK,gBAAgB,IAAIC,CAAK,GAAK,CAAC,EAChDC,EAAQF,EAAU,QAAQH,CAAQ,EACxC,OAAIK,IAAU,IACbF,EAAU,OAAOE,EAAO,CAAC,EAGtBF,EAAU,SAAW,GACxB,KAAK,gBAAgB,OAAOC,CAAK,EAG3B,IACR,CAQO,KAAKA,KAA2BF,EAA4B,CAClE,IAAII,EAAS,GACPH,EAAY,KAAK,gBAAgB,IAAIC,CAAK,EAEhD,GAAID,GAAaA,EAAU,OAAS,EACnC,QAAWH,KAAYG,EAEtBH,EAAS,GAAGE,CAAU,EACtBI,EAAS,GAIX,OAAOA,CACR,CAOO,UAAUF,EAAgC,CAChD,OAAO,KAAK,gBAAgB,IAAIA,CAAK,GAAK,CAAC,CAC5C,CAOO,mBAAmBA,EAA+B,CACxD,OAAIA,EACH,KAAK,gBAAgB,OAAOA,CAAK,EAEjC,KAAK,gBAAgB,MAAM,EAGrB,IACR,CAOO,gBAAgBG,EAAiB,CACvC,KAAK,cAAgBA,EACrB,QAAWJ,KAAa,KAAK,gBAAgB,OAAO,EAC/CA,EAAU,OAASI,GACtBJ,EAAU,OAAOI,CAAC,CAGrB,CAMO,iBAAmC,CACzC,IAAID,EAAS,IAAI,MACjB,QAAWH,KAAa,KAAK,gBAAgB,OAAO,EACnDG,EAASA,EAAO,OAAOH,CAAS,EAGjC,OAAOG,CACR,CACD,ECrYO,IAAME,EAAN,cAAwBC,CAAW,CAIzC,YAAYC,EAA4B,CACvC,MAAM,CAAC,OAAQA,GAAS,MAAM,CAAC,EAJhCC,EAAA,eACAA,EAAA,wBAAmB,IAIlB,KAAK,OAAS,IAAI,IAEdD,GAAS,kBAAoB,SAChC,KAAK,iBAAmBA,EAAQ,gBAElC,CAMA,IAAW,OAAQ,CAClB,OAAO,KAAK,MACb,CAMA,IAAW,iBAAkB,CAC5B,OAAO,KAAK,gBACb,CAMA,IAAW,gBAAgBE,EAAO,CACjC,KAAK,iBAAmBA,CACzB,CAMA,IAAW,QAA6B,CACvC,OAAO,KAAK,OACb,CAMA,IAAW,OAAOC,EAA4B,CAC7C,KAAK,QAAUA,CAChB,CAQO,OAAOC,EAAeC,EAAe,CAC3C,IAAMC,EAAgB,KAAK,OAAO,IAAIF,CAAK,EACvCE,EACHA,EAAc,KAAKD,CAAO,EAE1B,KAAK,OAAO,IAAID,EAAO,CAACC,CAAO,CAAC,CAElC,CAOO,QAAQE,EAAoB,CAClC,QAAWC,KAAQD,EAClB,KAAK,OAAOC,EAAK,MAAOA,EAAK,OAAO,CAEtC,CAQO,YAAYJ,EAAeC,EAAe,CAChD,IAAMC,EAAgB,KAAK,OAAO,IAAIF,CAAK,EACvCE,EACHA,EAAc,QAAQD,CAAO,EAE7B,KAAK,OAAO,IAAID,EAAO,CAACC,CAAO,CAAC,CAElC,CAOO,gBAAgBD,EAAeC,EAAe,CACpD,IAAMG,EAAO,SAAUC,KACtB,KAAK,WAAWL,EAAOI,CAAI,EAEpBH,EAAQ,GAAGI,CAAU,GAG7B,KAAK,YAAYL,EAAOI,CAAI,CAC7B,CAOO,SAASJ,EAAeC,EAAe,CAC7C,IAAMG,EAAO,SAAUC,KACtB,KAAK,WAAWL,EAAOI,CAAI,EAEpBH,EAAQ,GAAGI,CAAU,GAG7B,KAAK,OAAOL,EAAOI,CAAI,CACxB,CAQO,WAAWJ,EAAeC,EAAe,CAC/C,IAAMC,EAAgB,KAAK,OAAO,IAAIF,CAAK,EAC3C,GAAIE,EAAe,CAClB,IAAMI,EAAQJ,EAAc,QAAQD,CAAO,EACvCK,IAAU,IACbJ,EAAc,OAAOI,EAAO,CAAC,CAE/B,CACD,CAOO,YAAYH,EAAoB,CACtC,QAAWC,KAAQD,EAClB,KAAK,WAAWC,EAAK,MAAOA,EAAK,OAAO,CAE1C,CAQA,MAAa,KAAQJ,KAAkBK,EAAiB,CACvD,IAAMH,EAAgB,KAAK,OAAO,IAAIF,CAAK,EAC3C,GAAIE,EACH,QAAWD,KAAWC,EACrB,GAAI,CAEH,MAAMD,EAAQ,GAAGI,CAAU,CAC5B,OAASE,EAAO,CACf,IAAMC,EAAU,GAAGR,CAAK,KAAMO,EAAgB,OAAO,GAMrD,GALA,KAAK,KAAK,QAAS,IAAI,MAAMC,CAAO,CAAC,EACjC,KAAK,SACR,KAAK,QAAQ,MAAMA,CAAO,EAGvB,KAAK,iBACR,MAAM,IAAI,MAAMA,CAAO,CAEzB,CAGH,CAOO,SAASR,EAAe,CAC9B,OAAO,KAAK,OAAO,IAAIA,CAAK,CAC7B,CAMO,YAAa,CACnB,KAAK,OAAO,MAAM,CACnB,CACD", "names": ["Eventified", "options", "__publicField", "eventName", "listener", "onceListener", "arguments_", "listeners", "event", "index", "result", "n", "Hookified", "Eventified", "options", "__publicField", "value", "logger", "event", "handler", "eventHandlers", "hooks", "hook", "arguments_", "index", "error", "message"]}