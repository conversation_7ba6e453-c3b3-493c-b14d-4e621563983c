"use strict";var g=Object.defineProperty;var h=Object.getOwnPropertyDescriptor;var a=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var v=(n,e)=>{for(var t in e)g(n,t,{get:e[t],enumerable:!0})},c=(n,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of a(e))!u.call(n,r)&&r!==t&&g(n,r,{get:()=>e[r],enumerable:!(s=h(e,r))||s.enumerable});return n};var p=n=>c(g({},"__esModule",{value:!0}),n);var E={};v(E,{Eventified:()=>i,Hookified:()=>m});module.exports=p(E);var i=class{_eventListeners;_maxListeners;_logger;constructor(e){this._eventListeners=new Map,this._maxListeners=100,this._logger=e?.logger}once(e,t){let s=(...r)=>{this.off(e,s),t(...r)};return this.on(e,s),this}listenerCount(e){if(!e)return this.getAllListeners().length;let t=this._eventListeners.get(e);return t?t.length:0}eventNames(){return Array.from(this._eventListeners.keys())}rawListeners(e){return e?this._eventListeners.get(e)??[]:this.getAllListeners()}prependListener(e,t){let s=this._eventListeners.get(e)??[];return s.unshift(t),this._eventListeners.set(e,s),this}prependOnceListener(e,t){let s=(...r)=>{this.off(e,s),t(...r)};return this.prependListener(e,s),this}maxListeners(){return this._maxListeners}addListener(e,t){return this.on(e,t),this}on(e,t){this._eventListeners.has(e)||this._eventListeners.set(e,[]);let s=this._eventListeners.get(e);return s&&(s.length>=this._maxListeners&&console.warn(`MaxListenersExceededWarning: Possible event memory leak detected. ${s.length+1} ${e} listeners added. Use setMaxListeners() to increase limit.`),s.push(t)),this}removeListener(e,t){return this.off(e,t),this}off(e,t){let s=this._eventListeners.get(e)??[],r=s.indexOf(t);return r!==-1&&s.splice(r,1),s.length===0&&this._eventListeners.delete(e),this}emit(e,...t){let s=!1,r=this._eventListeners.get(e);if(r&&r.length>0)for(let o of r)o(...t),s=!0;return s}listeners(e){return this._eventListeners.get(e)??[]}removeAllListeners(e){return e?this._eventListeners.delete(e):this._eventListeners.clear(),this}setMaxListeners(e){this._maxListeners=e;for(let t of this._eventListeners.values())t.length>e&&t.splice(e)}getAllListeners(){let e=new Array;for(let t of this._eventListeners.values())e=e.concat(t);return e}};var m=class extends i{_hooks;_throwHookErrors=!1;constructor(e){super({logger:e?.logger}),this._hooks=new Map,e?.throwHookErrors!==void 0&&(this._throwHookErrors=e.throwHookErrors)}get hooks(){return this._hooks}get throwHookErrors(){return this._throwHookErrors}set throwHookErrors(e){this._throwHookErrors=e}get logger(){return this._logger}set logger(e){this._logger=e}onHook(e,t){let s=this._hooks.get(e);s?s.push(t):this._hooks.set(e,[t])}onHooks(e){for(let t of e)this.onHook(t.event,t.handler)}prependHook(e,t){let s=this._hooks.get(e);s?s.unshift(t):this._hooks.set(e,[t])}prependOnceHook(e,t){let s=async(...r)=>(this.removeHook(e,s),t(...r));this.prependHook(e,s)}onceHook(e,t){let s=async(...r)=>(this.removeHook(e,s),t(...r));this.onHook(e,s)}removeHook(e,t){let s=this._hooks.get(e);if(s){let r=s.indexOf(t);r!==-1&&s.splice(r,1)}}removeHooks(e){for(let t of e)this.removeHook(t.event,t.handler)}async hook(e,...t){let s=this._hooks.get(e);if(s)for(let r of s)try{await r(...t)}catch(o){let l=`${e}: ${o.message}`;if(this.emit("error",new Error(l)),this._logger&&this._logger.error(l),this._throwHookErrors)throw new Error(l)}}getHooks(e){return this._hooks.get(e)}clearHooks(){this._hooks.clear()}};0&&(module.exports={Eventified,Hookified});
