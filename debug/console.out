background-api.js:131 Fetch finished loading: POST "https://api.openai.com/v1/chat/completions".
startStream @ background-api.js:131
streamLLMResponse @ background-api.js:210
handleStreamRequest @ background-ports.js:111
await in handleStreamRequest
(anonymous) @ background-ports.js:81
background-api.js:131 Fetch finished loading: POST "https://api.openai.com/v1/chat/completions".
startStream @ background-api.js:131
streamLLMResponse @ background-api.js:210
handleStreamRequest @ background-ports.js:111
await in handleStreamRequest
(anonymous) @ background-ports.js:81
background-api.js:131 Fetch finished loading: POST "https://api.openai.com/v1/chat/completions".
startStream @ background-api.js:131
streamLLMResponse @ background-api.js:210
handleStreamRequest @ background-ports.js:111
await in handleStreamRequest
(anonymous) @ background-ports.js:81
