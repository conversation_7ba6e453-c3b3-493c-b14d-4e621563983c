# Streaming was broken during refactoring - files were too large and therefore split into smaller files.

- Streaming functions exist in @/scripts/background-api.js

1. Trace the required data flow of communication via the (formerly working) streaming functions
2. Plan the reconnection of the data flow via the streaming request and streaming response functions, when streaming is enabled in the configuration.


