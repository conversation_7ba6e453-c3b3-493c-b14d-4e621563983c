// In streamLLMResponse function:
async function streamLLMResponse(message, port) {
  // Add timeout for settings loading
  const settingsTimeout = setTimeout(() => {
    port.postMessage({ type: 'error', error: 'Settings load timeout' });
    return;
  }, 5000); // 5 seconds

  chrome.storage.sync.get(Object.keys(config), async (loadedSettings) => {
    clearTimeout(settingsTimeout);
    
    // Add validation for streaming prerequisites
    if (loadedSettings.streamResponses === false) {
      port.postMessage({ type: 'error', error: 'Streaming disabled in settings' });
      return;
    }

    // Add connection timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds

    try {
      // Modified fetch request with better error handling
      const response = await fetch(loadedSettings.endpoint, {
        method: 'POST',
        headers: {
          ...headers,
          'Connection': 'keep-alive', // Ensure persistent connection
          'Cache-Control': 'no-cache' // Prevent caching issues
        },
        body: JSON.stringify({
          ...requestBody,
          stream: true // Force streaming mode
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // Add HTTP status validation
      if (!response.ok || !response.body) {
        throw new Error(`HTTP ${response.status} - ${await response.text()}`);
      }

      // Rest of streaming logic...
    } catch (error) {
      // Improved error handling
      const errorMessage = error.name === 'AbortError' ? 
        'Connection timeout - check endpoint availability' :
        `Network error: ${error.message}`;
        
      port.postMessage({ type: 'error', error: errorMessage });
      // Add retry logic here if appropriate
    }
  });
}

// How to Fix Streaming in Universal AI Connector

This guide describes the step-by-step changes required to enable true streaming (typewriter effect) of LLM responses in the extension. The backend (`background.js`) already supports streaming, but the frontend (`panel.js`) must be updated to:
- Establish a persistent port connection
- Listen for streamed tokens
- Incrementally update the UI as tokens arrive

---

## 1. Add Port Connection to `panel.js`

At the top of `panel.js`, add:

```javascript
let port = null;
let isStreamingResponse = false;
let currentStreamMessageId = null;
let accumulatedStreamContent = '';
```

Then, add this function to establish the port connection:

```javascript
function setupPortConnection() {
  if (port) port.disconnect();
  try {
    port = chrome.runtime.connect({ name: 'panel-port' });
    port.onMessage.addListener(handleStreamResponse);
    port.onDisconnect.addListener(() => {
      port = null;
      if (isStreamingResponse) finishStreamingResponse('Connection lost. Please try again.');
    });
  } catch (err) {
    port = null;
  }
}
```

Call `setupPortConnection()` in your `DOMContentLoaded` handler:

```javascript
document.addEventListener('DOMContentLoaded', () => {
  // ...
  setupPortConnection();
  // ...
});
```

---

## 2. Handle Streaming Responses

Add the following functions to handle streamed tokens and UI updates:

```javascript
function handleStreamResponse(message) {
  switch (message.type) {
    case 'token':
      if (!isStreamingResponse) isStreamingResponse = true;
      accumulatedStreamContent += message.token;
      updateStreamingMessage(accumulatedStreamContent);
      break;
    case 'complete':
      finishStreamingResponse();
      break;
    case 'error':
      finishStreamingResponse(message.error);
      break;
  }
}

function updateStreamingMessage(content) {
  const messageElement = document.querySelector(`.message[data-id="${currentStreamMessageId}"]`);
  if (messageElement) {
    const contentElement = messageElement.querySelector('.message-content');
    if (contentElement) {
      if (typeof marked !== 'undefined') {
        contentElement.innerHTML = marked.parse(content);
      } else {
        contentElement.textContent = content;
      }
      handleNewMessage(messageElement); // for code copy buttons etc
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  }
}

function finishStreamingResponse(errorMessage = null) {
  isStreamingResponse = false;
  if (errorMessage) {
    removeMessage(currentStreamMessageId);
    addMessage(`Error: ${errorMessage}`, 'system');
    addSettingsLink();
  } else {
    chatHistory.push({ role: 'assistant', content: accumulatedStreamContent });
  }
  currentStreamMessageId = null;
  accumulatedStreamContent = '';
}
```

---

## 3. Update `sendMessage` to Use Streaming

Modify your `sendMessage` function so that, if streaming is enabled and the port is available, it sends the message via the port and displays the response as a stream:

```javascript
function sendMessage() {
  const userMessage = userInput.value.trim();
  if (!userMessage) return;
  addMessage(userMessage, 'user');
  userInput.value = '';
  chatHistory.push({ role: 'user', content: userMessage });

  const useStreaming = config.streamResponses !== false && port !== null;
  if (useStreaming) {
    currentStreamMessageId = addMessage('', 'assistant');
    accumulatedStreamContent = '';
    try {
      port.postMessage({
        action: 'streamChatMessage',
        message: userMessage,
        history: chatHistory.slice(0, -1),
        context: pageContext,
        selectedText: selectedText
      });
    } catch (err) {
      finishStreamingResponse('Failed to send message. Please try again.');
    }
  } else {
    // Fallback to non-streaming logic
    const thinkingId = addMessage('Thinking...', 'assistant');
    // ...
  }
}
```

---

## 4. Summary of Steps

- Add global state variables for streaming.
- Implement `setupPortConnection`, `handleStreamResponse`, `updateStreamingMessage`, and `finishStreamingResponse` in `panel.js`.
- Update `sendMessage` to use the port for streaming if enabled.
- Call `setupPortConnection()` on extension startup.

**After these changes, the UI will progressively update as tokens arrive, giving a true streaming/typewriter effect.**

If you encounter issues, check the background script for port naming and ensure the backend is sending `token`, `complete`, and `error` messages as expected.
