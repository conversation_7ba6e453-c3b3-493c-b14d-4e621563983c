
# This is an example of code that will allow the data returned by the AI endpoint to be downloaded by the user

```html
<button id="download-btn" style="background-color: #012c5a; color: #FFFFFF; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Download Response</button>
```

```javascript
document.getElementById('download-btn').addEventListener('click', function() {
  // Replace this with your actual markdown content variable
  const markdownContent = getYourMarkdownContentSomehow(); // e.g., a variable or function
  
  const blob = new Blob([markdownContent], { type: 'text/markdown' });
  const url = window.URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = 'response.md'; // You can change the filename as needed
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
});
```
