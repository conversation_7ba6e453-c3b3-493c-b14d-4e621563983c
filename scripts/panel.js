// Import modules
import { debugLog, debugError } from './panel-debug.js';
import { initializeMarked } from './panel-markdown.js';
import {
  loadThemePreference,
  toggleDarkMode,
  setupUIEventListeners,
  showErrorToUser as uiShowErrorToUser,
  exportChat,
  getMessagesContainer,
  addMessage,
  removeMessage,
  addCopyButtonsToCodeBlocks,
  updateUIAfterClear,
  clearMarkdownCache
} from './panel-ui.js';
import { initializeChat } from './panel-chat.js';
import { setupPortConnection, sendMessage } from './panel-communication.js';
import { checkForPageContext, clearPageContext, startContextUpdates, enableContext, isContextEnabled } from './panel-context.js';
import cacheManager from './cache-manager.js';

// State
let isStreamingResponse = false;
let currentStreamMessageId = null;
let accumulatedStreamContent = '';

/**
 * Clear the chat history and all caches
 */
async function clearChat() {
  try {
    // Stop any ongoing streaming response before clearing chat
    if (isStreamingResponse && currentStreamMessageId) {
      finishStreamingResponse();
    }

    // Use the updateUIAfterClear function to reset UI elements
    updateUIAfterClear();

    // Clear all caches using the cache manager first
    const results = cacheManager.clearAll();
    debugLog('All caches cleared:', results);

    // Clear markdown cache
    clearMarkdownCache();

    // Clear page context - this must be done AFTER clearing caches
    clearPageContext();

    // Notify the background script to clear both chat history and context
    await Promise.all([
      sendMessage({ action: 'clearHistory' }),
      sendMessage({ action: 'clearContext' }),
      sendMessage({ action: 'clearAllCaches' })
    ]).catch(error => {
      debugError('Error clearing background caches:', error);
      // Continue even if background clearing fails
    });

    debugLog('Chat and context cleared successfully');
  } catch (error) {
    debugError('Error clearing chat:', error);
    uiShowErrorToUser('Failed to clear chat. Please try again.');
  }
}

/**
 * Start a new chat session
 */
async function newChat() {
  try {
    // Stop any ongoing streaming response before clearing chat
    if (isStreamingResponse && currentStreamMessageId) {
      finishStreamingResponse();
    }

    // Clear existing messages and chat history
    await clearChat();

    // Explicitly enable context usage - must be done after clearChat
    enableContext();

    // Restart context updates - must be done after enabling context
    startContextUpdates();

    // Refresh the page context by re-reading the DOM
    const refreshedContext = await checkForPageContext();

    // Update UI to indicate the context has been refreshed
    if (refreshedContext && refreshedContext.content) {
      const pageInfo = document.getElementById('page-info');
      if (pageInfo) {
        const contentLength = refreshedContext.content.length;
        const tokenEstimate = Math.round(contentLength / 4); // Rough estimate: 4 chars per token
        pageInfo.textContent = `Using current page as context (~${tokenEstimate} tokens)`;
      }

      // Update selection info if there's a selection
      const selectionInfo = document.getElementById('selection-info');
      if (selectionInfo) {
        if (refreshedContext.selection) {
          selectionInfo.textContent = 'Using selected text as context';
          selectionInfo.classList.remove('hidden');
        } else {
          selectionInfo.classList.add('hidden');
        }
      }

      debugLog('Page context refreshed:', {
        title: refreshedContext.title,
        contentLength: refreshedContext.content.length,
        hasSelection: !!refreshedContext.selection
      });
    } else {
      // Update UI to show that we're starting with a clean context
      const pageInfo = document.getElementById('page-info');
      if (pageInfo) {
        pageInfo.textContent = 'Using current page as context';
      }
    }

    // Add welcome message
    const welcomeMessage = "Hello! I can help answer questions about this page or any other topic. What would you like to know?";
    addMessage(welcomeMessage, 'system');

    debugLog('New chat started with refreshed context');
  } catch (error) {
    debugError('Error starting new chat:', error);
    uiShowErrorToUser('Failed to start new chat. Please try again.');
  }
}

/**
 * Handle streamed responses
 */
function handleStreamResponse(message) {
  debugLog('PANEL.JS: handleStreamResponse - Received stream message:', message);

  switch (message.type) {
    case 'token':
      if (!isStreamingResponse) isStreamingResponse = true;
      debugLog('PANEL.JS: handleStreamResponse - Received token:', message.token, typeof message.token);
      accumulatedStreamContent += message.token;
      debugLog('PANEL.JS: handleStreamResponse - Accumulated content:', accumulatedStreamContent);
      updateStreamingMessage(accumulatedStreamContent);
      break;

    // Support both original and new message types
    case 'streamChunk':
      if (!isStreamingResponse) isStreamingResponse = true;
      debugLog('PANEL.JS: handleStreamResponse - Received streamChunk:', message.chunk?.length || 0, 'bytes');
      if (message.chunk) {
        accumulatedStreamContent += message.chunk;
        updateStreamingMessage(accumulatedStreamContent);
      }
      break;

    case 'complete':
    case 'streamComplete':
      debugLog('PANEL.JS: handleStreamResponse - Received complete signal.');
      // If there's a fullResponse, use that instead of accumulated content
      if (message.fullResponse) {
        accumulatedStreamContent = message.fullResponse;
      }
      updateStreamingMessage(accumulatedStreamContent);
      finishStreamingResponse();
      break;

    case 'error':
    case 'streamError':
      debugError('PANEL.JS: handleStreamResponse - Received error signal:', message.error);
      finishStreamingResponse(message.error);
      break;
  }
}

/**
 * Update the UI with streamed content
 */
function updateStreamingMessage(content) {
  const messageElement = document.querySelector(`#${currentStreamMessageId}`);
  debugLog('PANEL.JS: updateStreamingMessage - Attempting update. currentStreamMessageId:', currentStreamMessageId, 'Found messageElement:', messageElement !== null);

  if (messageElement) {
    const contentElement = messageElement.querySelector('.message-content');
    debugLog('PANEL.JS: updateStreamingMessage - Found contentElement:', contentElement !== null);

    if (contentElement) {
      const markedToUse = initializeMarked();
      const htmlContent = markedToUse.parse(content);

      debugLog('PANEL.JS: updateStreamingMessage - Output of marked.parse() (type: ' + typeof htmlContent + ' ): "' + String(htmlContent).substring(0,200) + '..."' );
      contentElement.innerHTML = htmlContent;

      // Force a reflow to ensure the DOM updates
      void contentElement.offsetHeight;

      // Apply highlighting to any new code blocks
      if (typeof hljs !== 'undefined') {
        contentElement.querySelectorAll('pre code').forEach((block) => {
          if (!block.classList.contains('hljs') || !block.dataset.highlighted) {
            try {
              const language = block.className.replace('language-', '');
              const codeText = block.textContent || '';
              debugLog('PANEL.JS: Highlighting code block with language:', language, 'text type:', typeof codeText);

              if (codeText) {
                const result = hljs.highlight(codeText, { language: language || 'plaintext' });
                if (result && result.value) {
                  block.innerHTML = result.value;
                  block.classList.add('hljs');
                  block.dataset.highlighted = 'true';
                }
              }
            } catch (e) {
              debugError('PANEL.JS: Error highlighting code block:', e);
            }
          }
        });
      }

      // Scroll to bottom if the user hasn't scrolled up
      const messagesContainer = getMessagesContainer();
      if (messagesContainer && isScrolledToBottom(messagesContainer)) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      // Add copy buttons to any new code blocks
      addCopyButtonsToCodeBlocks(contentElement);

      // Force a redraw of the panel
      window.requestAnimationFrame(() => {
        contentElement.style.display = 'none';
        void contentElement.offsetHeight; // Force reflow
        contentElement.style.display = '';
      });
    }
  }
}

/**
 * Check if an element is scrolled to the bottom
 */
function isScrolledToBottom(container) {
  if (!container) return true;
  return Math.abs(container.scrollHeight - container.clientHeight - container.scrollTop) < 2;
}

/**
 * Finish streaming response and update state
 */
function finishStreamingResponse(errorMessage = null) {
  isStreamingResponse = false;
  if (errorMessage) {
    removeMessage(currentStreamMessageId);
    addMessage(`Error: ${errorMessage}`, 'system');
  } else {
    // Final update to ensure everything is rendered correctly
    const messageElement = document.getElementById(currentStreamMessageId);
    if (messageElement) {
      const contentElement = messageElement.querySelector('.message-content');
      if (contentElement) {
        // Force a final refresh of code blocks
        addCopyButtonsToCodeBlocks(contentElement);
      }
    }
  }
  currentStreamMessageId = null;
  accumulatedStreamContent = '';
}

/**
 * Load configuration from storage and update UI elements
 */
async function loadConfig() {
  try {
    // Get the label from storage
    const result = await chrome.storage.sync.get(['label']);
    const label = result.label || 'CWCS AI Connector';

    // Update the panel title
    const panelTitle = document.getElementById('panel-title');
    if (panelTitle) {
      panelTitle.textContent = label;
    }

    // Update the document title
    document.title = label;

    // Update the brand text if it exists (for consistency)
    const brandText = document.querySelector('.brand-text');
    if (brandText) {
      brandText.textContent = label;
    }

    debugLog('Configuration loaded and UI updated with label:', label);
  } catch (error) {
    debugError('Error loading configuration:', error);
  }
}

/**
 * Initialize the panel with all necessary functionality
 */
function initializePanel() {
  try {
    const messagesContainer = getMessagesContainer();
    if (!messagesContainer) {
      throw new Error('Messages container not found');
    }

    // Initialize markdown processing
    initializeMarked();

    // Load configuration and update UI
    loadConfig();

    // Set up theme
    loadThemePreference().then(isDark => {
      const themeToggle = document.getElementById('theme-toggle');
      if (themeToggle) {
        // Update the button text/icon based on current theme
        themeToggle.innerHTML = isDark ? '☀️' : '🌙';
        themeToggle.title = isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode';

        // Add click event listener
        themeToggle.addEventListener('click', () => {
          // Get current theme and toggle it
          const currentTheme = document.documentElement.getAttribute('data-theme');
          const newIsDark = currentTheme !== 'dark';
          toggleDarkMode(newIsDark);

          // Update button text/icon
          themeToggle.innerHTML = newIsDark ? '☀️' : '🌙';
          themeToggle.title = newIsDark ? 'Switch to Light Mode' : 'Switch to Dark Mode';
        });
      }
    }).catch(error => {
      debugError('Error loading theme preference:', error);
    });

    // Set up port connection to background script
    const port = setupPortConnection(handleStreamResponse);

    // Initialize chat functionality
    initializeChat();

    // Set up event listeners for UI elements
    setupUIEventListeners({
      onClearChat: clearChat,
      onNewChat: newChat,
      onExportChat: exportChat,
      onOpenSettings: () => chrome.runtime.openOptionsPage()
    });

    // Load page context after a short delay, but only if context is enabled
    setTimeout(() => {
      if (isContextEnabled()) {
        checkForPageContext().then(context => {
          if (context) {
            debugLog('Initial page context loaded', {
              title: context.title,
              contentLength: context.content?.length || 0,
              hasSelection: !!context.selection
            });
          }
        }).catch(error => {
          debugError('Error loading page context:', error);
        });
      } else {
        debugLog('Context is disabled, skipping initial context load');
      }
    }, 100);

    debugLog('Panel initialized');

  } catch (error) {
    debugError('Error initializing panel:', error);
    uiShowErrorToUser('Failed to initialize the panel. Please refresh the page or restart the browser.');
  }
}

// Initialize the panel when the DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializePanel);
} else {
  initializePanel();
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    initializePanel,
    loadConfig,
    clearChat,
    exportChat,
    handleStreamResponse,
    updateStreamingMessage,
    finishStreamingResponse
  };
}
