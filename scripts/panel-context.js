import { debugLog, debugError } from './panel-debug.js';
import { showToast } from './panel-ui.js';
import { sendMessage } from './panel-communication.js';
import cacheManager from './cache-manager.js';

// Flag to track if context should be used - will be loaded from storage
let contextEnabled = true;

// Storage key for context enabled state
const CONTEXT_ENABLED_KEY = 'contextEnabled';

// Store the current page context
let currentPageContext = {
  url: '',
  title: '',
  content: '',
  selection: '',
  timestamp: 0
};

// Register with cache manager
cacheManager.register(
  'pageContext',
  currentPageContext,
  (cache) => {
    // Reset the context object properties
    cache.url = '';
    cache.title = '';
    cache.content = '';
    cache.selection = '';
    cache.timestamp = 0;
  },
  'context'
);

/**
 * Check for page context from the current tab
 */
export async function checkForPageContext() {
  try {
    // Get the current tab
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab || !tab.id) {
      debugError('No active tab found');
      return null;
    }

    // Check if we have a valid tab URL
    if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('edge://')) {
      debugLog('Skipping context check for browser page');
      return null;
    }

    // Update the current page context
    currentPageContext = {
      url: tab.url,
      title: tab.title || '',
      content: '',
      selection: '',
      timestamp: Date.now()
    };

    // Try to get the page content using the content script
    try {
      const response = await sendMessageToTab(tab.id, { action: 'getPageContent' });

      if (response?.content) {
        currentPageContext.content = response.content;
        currentPageContext.title = response.title || currentPageContext.title;
        currentPageContext.selection = response.selection || '';
        currentPageContext.timestamp = Date.now();

        debugLog('Retrieved page context:', {
          title: currentPageContext.title,
          contentLength: currentPageContext.content.length,
          hasSelection: !!currentPageContext.selection
        });

        return currentPageContext;
      }
    } catch (error) {
      debugError('Error getting page content from content script:', error);
    }

    // Fallback: Try to extract content using the DOM API (for pages where content script might not be injected)
    try {
      const [result] = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          // Try to get the main content of the page
          const getPageContent = () => {
            // Try common content selectors
            const selectors = [
              'article',
              'main',
              '[role="main"]',
              '.content',
              '.main',
              '.post',
              '.article',
              'body'
            ];

            for (const selector of selectors) {
              const element = document.querySelector(selector);
              if (element && element.textContent.trim().length > 100) {
                return element.textContent.trim();
              }
            }

            // Fall back to body if no specific content found
            return document.body?.textContent?.trim() || '';
          };

          return {
            title: document.title,
            content: getPageContent(),
            url: window.location.href,
            selection: window.getSelection()?.toString()?.trim() || ''
          };
        }
      });

      if (result?.result) {
        currentPageContext = {
          url: result.result.url || tab.url,
          title: result.result.title || tab.title || '',
          content: result.result.content || '',
          selection: result.result.selection || '',
          timestamp: Date.now()
        };

        debugLog('Retrieved page context via DOM API:', {
          title: currentPageContext.title,
          contentLength: currentPageContext.content.length,
          hasSelection: !!currentPageContext.selection
        });

        return currentPageContext;
      }
    } catch (error) {
      debugError('Error extracting content via DOM API:', error);
    }

    return null;

  } catch (error) {
    debugError('Error checking for page context:', error);
    return null;
  }
}

/**
 * Get the current page context
 * @returns {Object} - The current page context
 */
export function getPageContext() {
  // If context is disabled, return an empty context
  if (!contextEnabled) {
    debugLog('Context usage is disabled, returning empty context');
    return {
      url: '',
      title: '',
      content: '',
      selection: '',
      timestamp: 0
    };
  }

  return { ...currentPageContext };
}

/**
 * Update the current page context
 * @param {Object} updates - The updates to apply to the context
 */
export function updatePageContext(updates) {
  currentPageContext = {
    ...currentPageContext,
    ...updates,
    timestamp: Date.now()
  };

  debugLog('Updated page context:', {
    title: currentPageContext.title,
    contentLength: currentPageContext.content?.length || 0,
    hasSelection: !!currentPageContext.selection
  });

  // Notify the background script about the context update
  if (currentPageContext.content) {
    sendMessage({
      type: 'updateContext',
      context: currentPageContext
    }).catch(error => {
      debugError('Error sending context update to background:', error);
    });
  }
}

/**
 * Clear the current page context
 */
export function clearPageContext() {
  // Reset the context object properties
  currentPageContext.url = '';
  currentPageContext.title = '';
  currentPageContext.content = '';
  currentPageContext.selection = '';
  currentPageContext.timestamp = 0;

  // Disable context usage
  contextEnabled = false;

  // Save the disabled state to storage so it persists across panel reopens
  try {
    chrome.storage.local.set({ [CONTEXT_ENABLED_KEY]: false });
  } catch (storageError) {
    debugError('Error saving context disabled state to storage:', storageError);
  }

  // Use the cache manager to clear the context
  try {
    cacheManager.clearCache('pageContext');
  } catch (cacheError) {
    debugError('Error clearing page context cache:', cacheError);
    // Continue execution even if cache clearing fails
  }

  // Stop periodic context updates to prevent automatic reloading
  stopContextUpdates();

  // Notify the background script - use a try/catch to prevent page reload
  try {
    sendMessage({
      action: 'clearContext'
    }).catch(error => {
      debugError('Error clearing context in background:', error);
    });
  } catch (error) {
    debugError('Failed to send clearContext message:', error);
    // Continue execution even if this fails
  }

  debugLog('Cleared page context, disabled context usage, and stopped automatic updates');

  // Notify tabs about the context clear - use try/catch to prevent page reload
  try {
    chrome.tabs.query({}, (tabs) => {
      if (chrome.runtime.lastError) {
        debugError('Error querying tabs:', chrome.runtime.lastError);
        return;
      }

      tabs.forEach((tab) => {
        if (tab && tab.id) {
          try {
            chrome.tabs.sendMessage(tab.id, { action: 'contextCleared' }, () => {
              if (chrome.runtime.lastError) {
                // Ignore errors from tabs that don't have content script
                debugLog(`Could not send to tab ${tab.id}: ${chrome.runtime.lastError.message}`);
              }
            });
          } catch (tabError) {
            debugError(`Error sending to tab ${tab.id}:`, tabError);
          }
        }
      });
    });
  } catch (error) {
    debugError('Failed to notify tabs about context clear:', error);
    // Continue execution even if this fails
  }
}

/**
 * Get the selected text from the current tab
 * @returns {Promise<string>} - The selected text, or an empty string if none
 */
export async function getSelectedText() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab?.id) return '';

    const [result] = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => window.getSelection()?.toString()?.trim() || ''
    });

    return result?.result || '';
  } catch (error) {
    debugError('Error getting selected text:', error);
    return '';
  }
}

/**
 * Send a message to a tab and handle the response
 * @param {number} tabId - The ID of the tab to send the message to
 * @param {Object} message - The message to send
 * @returns {Promise<any>} - The response from the tab
 */
async function sendMessageToTab(tabId, message) {
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(tabId, message, (response) => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
      } else {
        resolve(response);
      }
    });
  });
}

/**
 * Get the current tab's URL
 * @returns {Promise<string>} - The current tab's URL, or an empty string if not available
 */
export async function getCurrentTabUrl() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab?.url || '';
  } catch (error) {
    debugError('Error getting current tab URL:', error);
    return '';
  }
}

/**
 * Check if the current page is a browser page (chrome://, edge://, etc.)
 * @param {string} url - The URL to check
 * @returns {boolean} - True if the URL is a browser page
 */
export function isBrowserPage(url) {
  if (!url) return true;
  return url.startsWith('chrome://') ||
         url.startsWith('edge://') ||
         url.startsWith('about:') ||
         url.startsWith('chrome-extension://') ||
         url.startsWith('edge-extension://');
}

// Periodically check for context updates
let contextCheckInterval;

/**
 * Start checking for context updates
 * @param {number} interval - The interval in milliseconds (default: 5 seconds)
 */
export function startContextUpdates(interval = 5000) {
  // Enable context usage
  contextEnabled = true;

  // Save the enabled state to storage so it persists across panel reopens
  try {
    chrome.storage.local.set({ [CONTEXT_ENABLED_KEY]: true });
  } catch (storageError) {
    debugError('Error saving context enabled state to storage:', storageError);
  }

  stopContextUpdates();

  // Initial check
  checkForPageContext().catch(error => {
    debugError('Error in initial context check:', error);
  });

  // Set up periodic checks
  contextCheckInterval = setInterval(() => {
    checkForPageContext().catch(error => {
      debugError('Error in periodic context check:', error);
    });
  }, interval);

  debugLog('Context updates started and context usage enabled');
}

/**
 * Stop checking for context updates
 */
export function stopContextUpdates() {
  if (contextCheckInterval) {
    clearInterval(contextCheckInterval);
    contextCheckInterval = null;
  }
}

/**
 * Load context enabled state from storage and initialize accordingly
 */
async function loadContextState() {
  try {
    const result = await chrome.storage.local.get([CONTEXT_ENABLED_KEY]);
    const storedEnabled = result[CONTEXT_ENABLED_KEY];

    // If no stored value exists, default to true (first time use)
    if (storedEnabled === undefined) {
      contextEnabled = true;
      // Save the default state
      chrome.storage.local.set({ [CONTEXT_ENABLED_KEY]: true });
    } else {
      contextEnabled = storedEnabled;
    }

    debugLog('Loaded context enabled state from storage:', contextEnabled);

    // Only start context updates if context is enabled
    if (contextEnabled) {
      startContextUpdates();
    } else {
      debugLog('Context is disabled, not starting automatic updates');
    }
  } catch (error) {
    debugError('Error loading context state from storage:', error);
    // Default to enabled if we can't load from storage
    contextEnabled = true;
    startContextUpdates();
  }
}

/**
 * Initialize context state - should be called when the panel loads
 */
export function initializeContextState() {
  loadContextState();
}

// Initialize context state when this module is loaded
initializeContextState();

/**
 * Enable context usage
 */
export function enableContext() {
  contextEnabled = true;

  // Save the enabled state to storage so it persists across panel reopens
  try {
    chrome.storage.local.set({ [CONTEXT_ENABLED_KEY]: true });
  } catch (storageError) {
    debugError('Error saving context enabled state to storage:', storageError);
  }

  debugLog('Context usage enabled');
}

/**
 * Disable context usage
 */
export function disableContext() {
  contextEnabled = false;

  // Save the disabled state to storage so it persists across panel reopens
  try {
    chrome.storage.local.set({ [CONTEXT_ENABLED_KEY]: false });
  } catch (storageError) {
    debugError('Error saving context disabled state to storage:', storageError);
  }

  debugLog('Context usage disabled');
}

/**
 * Check if context is enabled
 * @returns {boolean} - True if context is enabled
 */
export function isContextEnabled() {
  return contextEnabled;
}

// Clean up on page unload
window.addEventListener('beforeunload', () => {
  stopContextUpdates();
});
